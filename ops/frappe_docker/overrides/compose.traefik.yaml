version: "3.3"

services:
  traefik:
    image: "traefik:v2.11"
    restart: unless-stopped
    labels:
      # Enable Traefik for this service, to make it available in the public network
      - traefik.enable=true
      # Use the traefik-public network (declared below)
      - traefik.docker.network=traefik-public
      # admin-auth middleware with HTTP Basic auth
      # Using the environment variables USERNAME and HASHED_PASSWORD
      - traefik.http.middlewares.admin-auth.basicauth.users=admin:${HASHED_PASSWORD:?No HASHED_PASSWORD set}
      # Uses the environment variable TRAEFIK_DOMAIN
      - traefik.http.routers.traefik-public-http.rule=Host(`${TRAEFIK_DOMAIN:?No TRAEFIK_DOMAIN set}`)
      - traefik.http.routers.traefik-public-http.entrypoints=http
      # Use the special Traefik service api@internal with the web UI/Dashboard
      - traefik.http.routers.traefik-public-http.service=api@internal
      # Enable HTTP Basic auth, using the middleware created above
      - traefik.http.routers.traefik-public-http.middlewares=admin-auth
      # Define the port inside of the Docker service to use
      - traefik.http.services.traefik-public.loadbalancer.server.port=8080
    command:
      # Enable Docker in Traefik, so that it reads labels from Docker services
      - --providers.docker=true
      # Do not expose all Docker services, only the ones explicitly exposed
      - --providers.docker.exposedbydefault=false
      # Create an entrypoint http listening on port 80
      - --entrypoints.http.address=:80
      # Enable the access log, with HTTP requests
      - --accesslog
      # Enable the Traefik log, for configurations and errors
      - --log
      # Enable the Dashboard and API
      - --api
    ports:
      - ${HTTP_PUBLISH_PORT:-80}:80
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - traefik-public

networks:
  traefik-public:
    name: traefik-public
    external: false
