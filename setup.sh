#!/bin/bash
# AI Agent Setup Script
# This script installs all required dependencies and sets up console scripts

echo "Setting up AI Agent..."

# Activate Frappe environment - detect local vs server installation
# Check for local frappe-bench first (in home directory)
LOCAL_FRAPPE_ENV="$HOME/frappe-bench/env"
SERVER_FRAPPE_ENV="/home/<USER>/frappe-bench/env"

if [ -f "$LOCAL_FRAPPE_ENV/bin/activate" ]; then
    echo "Found local Frappe environment at $LOCAL_FRAPPE_ENV"
    echo "Activating local Frappe environment..."
    source "$LOCAL_FRAPPE_ENV/bin/activate"
fi
# Get the directory where this script is located
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# Update tg-crew.yml data_dir to absolute path next to setup.sh (idempotent)
TG_CREW_YML="$SCRIPT_DIR/tg-crew.yml"
ABS_DATA_DIR="$SCRIPT_DIR/TGCrewData"
if [ -f "$TG_CREW_YML" ]; then
    # Create the directory if missing
    mkdir -p "$ABS_DATA_DIR"
    # Only update if not already set to the desired absolute path
    if ! grep -q "^  data_dir: $ABS_DATA_DIR$" "$TG_CREW_YML"; then
        # Replace any existing data_dir line (handles indented line in global_settings)
        if grep -q "^  data_dir:" "$TG_CREW_YML"; then
            # Use awk to safely replace the data_dir line
            awk -v new_path="$ABS_DATA_DIR" '
                /^  data_dir:/ { print "  data_dir: " new_path; next }
                { print }
            ' "$TG_CREW_YML" > "$TG_CREW_YML.tmp" && mv "$TG_CREW_YML.tmp" "$TG_CREW_YML"
        else
            # Fallback: append to global_settings section if data_dir doesn't exist
            if grep -q "^global_settings:" "$TG_CREW_YML"; then
                awk -v new_path="$ABS_DATA_DIR" '
                    /^global_settings:/ { print; print "  data_dir: " new_path; next }
                    { print }
                ' "$TG_CREW_YML" > "$TG_CREW_YML.tmp" && mv "$TG_CREW_YML.tmp" "$TG_CREW_YML"
            else
                printf '\ndata_dir: %s\n' "$ABS_DATA_DIR" >> "$TG_CREW_YML"
            fi
        fi
        echo "Set data_dir in tg-crew.yml to $ABS_DATA_DIR"
    fi
fi

# Site name and logs
SITE_NAME=${SITE_NAME:-"32016-51127.bacloud.info"}
export FRAPPE_SITE="$SITE_NAME"

# Ensure log directories exist for Frappe loggers
LOG_DIR0="/home/<USER>/frappe-bench/apps/logs/"
#LOG_DIR0="/home/<USER>/logs"
LOG_DIR1="$SCRIPT_DIR/ai_agent/ai_agent/logs"
LOG_DIR2="$SCRIPT_DIR/ai_agent/telegram_integration/$SITE_NAME/logs"
mkdir -p "$LOG_DIR1" "$LOG_DIR2" "$LOG_DIR0"

echo "Using site: $FRAPPE_SITE"
echo "Ensured log dirs: $LOG_DIR1 and $LOG_DIR2"

# Install the package in development mode to register console scripts
echo "Installing AI Agent package in development mode..."
cd "$SCRIPT_DIR"
pip install -e .

echo "Setup complete!"
echo ""
echo "Available console scripts:"
echo "  reef       - Run reef CLI"
echo "  tg-bot     - Run Telegram bot"
echo ""
echo "You can now run 'reef' or 'tg-bot' from anywhere in the terminal."
