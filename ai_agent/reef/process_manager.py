"""Process management utilities for tracking worker states."""
import os
import json
import fcntl
import time
from pathlib import Path
from typing import Dict, Any, Optional

# Directory to store PID files
PID_DIR = Path.home() / ".reef"
PID_FILE = PID_DIR / "workers.pid"


def ensure_pid_dir() -> None:
    """Ensure the PID directory exists."""
    PID_DIR.mkdir(parents=True, exist_ok=True)


def save_worker_state(worker_name: str, state: Dict[str, Any]) -> None:
    """Save worker state to the PID file.
    
    Args:
        worker_name: Name of the worker
        state: Dictionary containing worker state
    """
    ensure_pid_dir()
    
    # Create the state if it doesn't exist
    states = {}
    if PID_FILE.exists():
        try:
            states = json.loads(PID_FILE.read_text())
        except (json.JSONDecodeError, IOError):
            states = {}
    
    # Update the state for this worker
    states[worker_name] = {
        **state,
        "timestamp": time.time()
    }
    
    # Write the state back to the file
    with open(PID_FILE, 'w') as f:
        fcntl.flock(f, fcntl.LOCK_EX)
        try:
            json.dump(states, f, indent=2)
        finally:
            fcntl.flock(f, fcntl.LOCK_UN)


def get_worker_state(worker_name: str) -> Optional[Dict[str, Any]]:
    """Get the current state of a worker.
    
    Args:
        worker_name: Name of the worker
        
    Returns:
        Dictionary containing worker state, or None if not found
    """
    if not PID_FILE.exists():
        return None
    
    try:
        with open(PID_FILE, 'r') as f:
            fcntl.flock(f, fcntl.LOCK_SH)
            try:
                states = json.load(f)
                return states.get(worker_name)
            finally:
                fcntl.flock(f, fcntl.LOCK_UN)
    except (json.JSONDecodeError, IOError):
        return None


def remove_worker_state(worker_name: str) -> None:
    """Remove a worker's state from the PID file.
    
    Args:
        worker_name: Name of the worker to remove
    """
    if not PID_FILE.exists():
        return
    
    try:
        with open(PID_FILE, 'r+') as f:
            fcntl.flock(f, fcntl.LOCK_EX)
            try:
                states = json.load(f)
                if worker_name in states:
                    del states[worker_name]
                    f.seek(0)
                    f.truncate()
                    json.dump(states, f, indent=2)
            finally:
                fcntl.flock(f, fcntl.LOCK_UN)
    except (json.JSONDecodeError, IOError):
        pass


def get_all_worker_states() -> Dict[str, Dict[str, Any]]:
    """Get states for all workers.
    
    Returns:
        Dictionary mapping worker names to their states
    """
    if not PID_FILE.exists():
        return {}
    
    try:
        with open(PID_FILE, 'r') as f:
            fcntl.flock(f, fcntl.LOCK_SH)
            try:
                return json.load(f)
            finally:
                fcntl.flock(f, fcntl.LOCK_UN)
    except (json.JSONDecodeError, IOError):
        return {}
