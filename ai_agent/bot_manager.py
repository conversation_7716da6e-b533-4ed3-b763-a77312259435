import frappe
import subprocess
import os
import signal
import psutil

# File to store the bot's PID
BOT_PID_FILE = frappe.get_site_path('bot.pid')

# Path to the bot shell script
BOT_SCRIPT = os.path.join(frappe.get_app_path('ai_agent'), 'telegram_integration', 'run_script.sh')

@frappe.whitelist()
def is_bot_running():
    """Check if the bot is running by verifying the PID file."""
    if os.path.exists(BOT_PID_FILE):
        try:
            with open(BOT_PID_FILE, 'r') as f:
                pid = int(f.read())
            if psutil.pid_exists(pid):
                os.kill(pid, 0)  # Check if process is running
                return {"running": True}
            else:
                os.remove(BOT_PID_FILE)
                return {"running": False, "message": "Process not found, PID file removed"}
        except (ProcessLookupError, PermissionError):
            if os.path.exists(BOT_PID_FILE):
                os.remove(BOT_PID_FILE)
            return {"running": False, "message": "Process not found or permission denied, PID file removed"}
    return {"running": False}

@frappe.whitelist()
def toggle_bot():
    """Start or stop the bot based on its current state."""
    if os.path.exists(BOT_PID_FILE):
        with open(BOT_PID_FILE, 'r') as f:
            pid = int(f.read())
        try:
            if psutil.pid_exists(pid):
                os.kill(pid, signal.SIGTERM)
                os.remove(BOT_PID_FILE)
                return "Bot stopped"
            else:
                os.remove(BOT_PID_FILE)
                return "Process not found, PID file removed"
        except (ProcessLookupError, PermissionError):
            if os.path.exists(BOT_PID_FILE):
                os.remove(BOT_PID_FILE)
            return "Process not found or permission denied, PID file removed"
    else:
        try:
            # Ensure the script has executable permissions
            os.chmod(BOT_SCRIPT, 0o755)
            # Set environment to include Bench's virtual environment
            env = os.environ.copy()
            env['PATH'] = f"/home/<USER>/frappe-bench/env/bin:{env['PATH']}"
            proc = subprocess.Popen([BOT_SCRIPT], env=env, shell=False)
            with open(BOT_PID_FILE, 'w') as f:
                f.write(str(proc.pid))
            return f"Bot started with PID: {proc.pid}"
        except FileNotFoundError:
            return f"Error: run_script.sh not found at {BOT_SCRIPT}"
        except PermissionError:
            return f"Error: Permission denied for run_script.sh at {BOT_SCRIPT}. Ensure it has executable permissions."
        except Exception as e:
            return f"Error starting bot: {str(e)}"
