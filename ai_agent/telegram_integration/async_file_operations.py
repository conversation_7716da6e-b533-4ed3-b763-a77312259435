"""
Async file I/O operations for telegram integration.
Replaces synchronous file operations to prevent blocking the event loop.
"""

import asyncio
import aiofiles
import json
import os
import time
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime
from pathlib import Path
import threading

logger = logging.getLogger(__name__)

class AsyncMessageLogger:
    """
    Buffered async message logger that batches log entries to reduce file I/O frequency.
    """
    
    def __init__(self, log_file: str = 'messages.json', buffer_size: int = 100, flush_interval: float = 30.0):
        self.log_file = log_file
        self.buffer_size = buffer_size
        self.flush_interval = flush_interval
        self.buffer = []
        self.last_flush = time.time()
        self.buffer_lock = asyncio.Lock()
        self._flush_task = None
        self._start_auto_flush()
    
    def _start_auto_flush(self):
        """Start the auto-flush background task."""
        if self._flush_task is None or self._flush_task.done():
            self._flush_task = asyncio.create_task(self._auto_flush_loop())
    
    async def _auto_flush_loop(self):
        """Background task that flushes the buffer periodically."""
        while True:
            try:
                await asyncio.sleep(self.flush_interval)
                if self.buffer:
                    await self.flush_buffer()
            except Exception as e:
                logger.error(f"Error in auto-flush loop: {e}")
    
    async def log_message(self, message_data: Dict[str, Any]) -> None:
        """Add a message to the buffer for logging."""
        async with self.buffer_lock:
            # Add timestamp if not present
            if 'logged_at' not in message_data:
                message_data['logged_at'] = datetime.utcnow().isoformat()
            
            self.buffer.append(message_data)
            
            # Check if we need to flush
            should_flush = (
                len(self.buffer) >= self.buffer_size or
                time.time() - self.last_flush > self.flush_interval
            )
            
        if should_flush:
            await self.flush_buffer()
    
    async def flush_buffer(self) -> int:
        """Flush all buffered messages to disk."""
        if not self.buffer:
            return 0
        
        async with self.buffer_lock:
            messages_to_write = self.buffer.copy()
            self.buffer.clear()
            self.last_flush = time.time()
        
        try:
            # Ensure directory exists
            os.makedirs(os.path.dirname(self.log_file) if os.path.dirname(self.log_file) else '.', exist_ok=True)
            
            # Write messages to file asynchronously
            async with aiofiles.open(self.log_file, 'a', encoding='utf-8') as f:
                for message in messages_to_write:
                    await f.write(json.dumps(message, ensure_ascii=False) + '\n')
            
            logger.debug(f"Flushed {len(messages_to_write)} messages to {self.log_file}")
            return len(messages_to_write)
            
        except Exception as e:
            logger.error(f"Error flushing message buffer to {self.log_file}: {e}")
            # Put messages back in buffer for retry
            async with self.buffer_lock:
                self.buffer = messages_to_write + self.buffer
            raise
    
    async def close(self):
        """Close the logger and flush any remaining messages."""
        if self._flush_task and not self._flush_task.done():
            self._flush_task.cancel()
            try:
                await self._flush_task
            except asyncio.CancelledError:
                pass
        
        await self.flush_buffer()

# Global message logger instance
_message_logger = None

def get_message_logger(log_file: str = 'messages.json') -> AsyncMessageLogger:
    """Get or create the global message logger instance."""
    global _message_logger
    if _message_logger is None or _message_logger.log_file != log_file:
        _message_logger = AsyncMessageLogger(log_file)
    return _message_logger

async def save_seen_messages_async(seen_messages: Dict[str, Any]) -> None:
    """
    Asynchronously save seen messages to file.
    Replaces the synchronous save_seen_messages function.
    """
    path = seen_messages.get("_file")
    if not path:
        logger.warning("No file path specified for seen messages")
        return
    
    try:
        # Create backup asynchronously
        backup_file = path + ".backup"
        if os.path.exists(path):
            async with aiofiles.open(path, 'r', encoding='utf-8') as src:
                content = await src.read()
            async with aiofiles.open(backup_file, 'w', encoding='utf-8') as dst:
                await dst.write(content)
        
        # Write new content asynchronously
        # Create a copy without the _file key for JSON serialization
        data_to_save = {k: v for k, v in seen_messages.items() if k != '_file'}
        
        async with aiofiles.open(path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(data_to_save, ensure_ascii=False, indent=2))
        
        logger.debug(f"Saved seen messages to {path}")
        
    except Exception as e:
        logger.error(f"Error saving seen messages to {path}: {e}")
        raise

async def load_seen_messages_async(seen_file: str) -> Dict[str, Any]:
    """
    Asynchronously load seen messages from file.
    Replaces the synchronous load_seen_messages function.
    """
    if not os.path.exists(seen_file):
        return {"messages": {}, "last_processed": {}, "_file": seen_file}
    
    try:
        async with aiofiles.open(seen_file, 'r', encoding='utf-8') as f:
            content = await f.read()
            data = json.loads(content)
        
        seen_messages = data.get("messages", {})
        last_processed = data.get("last_processed", {})
        
        # Add file path for future saves
        result = {
            "messages": seen_messages,
            "last_processed": last_processed,
            "_file": seen_file
        }
        
        logger.debug(f"Loaded {len(seen_messages)} seen messages from {seen_file}")
        return result
        
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in seen messages file {seen_file}: {e}")
        return {"messages": {}, "last_processed": {}, "_file": seen_file}
    except Exception as e:
        logger.error(f"Error loading seen messages from {seen_file}: {e}")
        return {"messages": {}, "last_processed": {}, "_file": seen_file}

async def write_config_file_async(config_data: Dict[str, Any], file_path: str) -> bool:
    """
    Asynchronously write configuration data to JSON file.
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(file_path), exist_ok=True)
        
        async with aiofiles.open(file_path, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(config_data, indent=4, ensure_ascii=False))
        
        logger.debug(f"Wrote config file: {file_path}")
        return True
        
    except Exception as e:
        logger.error(f"Error writing config file {file_path}: {e}")
        return False

async def read_config_file_async(file_path: str) -> Optional[Dict[str, Any]]:
    """
    Asynchronously read configuration data from JSON file.
    """
    if not os.path.exists(file_path):
        return None
    
    try:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
            
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in config file {file_path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error reading config file {file_path}: {e}")
        return None

async def append_to_file_async(file_path: str, content: str) -> bool:
    """
    Asynchronously append content to a file.
    """
    try:
        # Ensure directory exists
        os.makedirs(os.path.dirname(file_path) if os.path.dirname(file_path) else '.', exist_ok=True)
        
        async with aiofiles.open(file_path, 'a', encoding='utf-8') as f:
            await f.write(content)
        
        return True
        
    except Exception as e:
        logger.error(f"Error appending to file {file_path}: {e}")
        return False

async def ensure_directory_async(directory_path: str) -> bool:
    """
    Asynchronously ensure a directory exists.
    """
    try:
        # Use asyncio to run the directory creation in a thread pool
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, os.makedirs, directory_path, True)
        return True
    except Exception as e:
        logger.error(f"Error creating directory {directory_path}: {e}")
        return False

class AsyncFileManager:
    """
    Context manager for handling multiple async file operations.
    """
    
    def __init__(self):
        self.open_files = []
    
    async def __aenter__(self):
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        # Close any remaining open files
        for file_obj in self.open_files:
            try:
                await file_obj.close()
            except Exception as e:
                logger.error(f"Error closing file: {e}")
        self.open_files.clear()
    
    async def open_file(self, file_path: str, mode: str = 'r', encoding: str = 'utf-8'):
        """Open a file and track it for cleanup."""
        file_obj = await aiofiles.open(file_path, mode, encoding=encoding)
        self.open_files.append(file_obj)
        return file_obj

# Utility functions for backward compatibility
async def safe_json_write_async(data: Any, file_path: str) -> bool:
    """
    Safely write JSON data to file with error handling.
    """
    try:
        temp_file = file_path + '.tmp'
        
        # Write to temporary file first
        async with aiofiles.open(temp_file, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(data, ensure_ascii=False, indent=2))
        
        # Atomically replace the original file
        loop = asyncio.get_event_loop()
        await loop.run_in_executor(None, os.replace, temp_file, file_path)
        
        return True
        
    except Exception as e:
        logger.error(f"Error writing JSON to {file_path}: {e}")
        # Clean up temp file if it exists
        try:
            if os.path.exists(temp_file):
                os.remove(temp_file)
        except:
            pass
        return False

async def safe_json_read_async(file_path: str) -> Optional[Any]:
    """
    Safely read JSON data from file with error handling.
    """
    if not os.path.exists(file_path):
        return None
    
    try:
        async with aiofiles.open(file_path, 'r', encoding='utf-8') as f:
            content = await f.read()
            return json.loads(content)
            
    except json.JSONDecodeError as e:
        logger.error(f"Invalid JSON in file {file_path}: {e}")
        return None
    except Exception as e:
        logger.error(f"Error reading JSON from {file_path}: {e}")
        return None

# Performance monitoring
class AsyncFilePerformanceMonitor:
    """
    Monitor async file operation performance.
    """
    
    def __init__(self):
        self.operation_times = []
        self.operation_counts = {}
    
    async def time_operation(self, operation_name: str, coro):
        """Time an async file operation."""
        start_time = time.time()
        try:
            result = await coro
            success = True
        except Exception as e:
            result = None
            success = False
            raise
        finally:
            duration = time.time() - start_time
            self.operation_times.append(duration)
            
            if operation_name not in self.operation_counts:
                self.operation_counts[operation_name] = {'count': 0, 'total_time': 0, 'errors': 0}
            
            self.operation_counts[operation_name]['count'] += 1
            self.operation_counts[operation_name]['total_time'] += duration
            if not success:
                self.operation_counts[operation_name]['errors'] += 1
            
            if duration > 1.0:  # Log slow operations
                logger.warning(f"Slow async file operation: {operation_name} took {duration:.3f}s")
        
        return result
    
    def get_stats(self) -> Dict[str, Any]:
        """Get performance statistics."""
        stats = {
            'total_operations': len(self.operation_times),
            'average_time': sum(self.operation_times) / len(self.operation_times) if self.operation_times else 0,
            'max_time': max(self.operation_times) if self.operation_times else 0,
            'operations': {}
        }
        
        for op_name, op_data in self.operation_counts.items():
            stats['operations'][op_name] = {
                'count': op_data['count'],
                'average_time': op_data['total_time'] / op_data['count'] if op_data['count'] > 0 else 0,
                'error_rate': op_data['errors'] / op_data['count'] if op_data['count'] > 0 else 0
            }
        
        return stats

# Global performance monitor
_performance_monitor = AsyncFilePerformanceMonitor()

def get_file_performance_stats() -> Dict[str, Any]:
    """Get async file operation performance statistics."""
    return _performance_monitor.get_stats()
