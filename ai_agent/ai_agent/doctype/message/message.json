{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2025-07-08 09:30:46.633022", "doctype": "DocType", "engine": "InnoDB", "field_order": ["room_id", "agent", "sender_name", "sender_id", "social_media_channel", "column_break_jnih", "is_response_template", "message_id", "synk_id", "status", "timestamp", "receiver", "section_break_nesx", "text", "column_break_dbqu", "media_url", "media_html"], "fields": [{"fieldname": "room_id", "fieldtype": "Link", "in_list_view": 1, "label": "Room ID", "options": "Room"}, {"fieldname": "message_id", "fieldtype": "Data", "label": "Message ID"}, {"fieldname": "sender_name", "fieldtype": "Data", "in_list_view": 1, "label": "Sender Name"}, {"fieldname": "agent", "fieldtype": "Link", "in_list_view": 1, "label": "Agent", "options": "Agents"}, {"default": "0", "fieldname": "is_response_template", "fieldtype": "Check", "in_list_view": 1, "label": "Is Instruction"}, {"fieldname": "text", "fieldtype": "Data", "in_list_view": 1, "label": "Text"}, {"fieldname": "media_url", "fieldtype": "Data", "label": "Media URL"}, {"fieldname": "media_html", "fieldtype": "HTML"}, {"fieldname": "column_break_jnih", "fieldtype": "Column Break"}, {"fieldname": "section_break_nesx", "fieldtype": "Section Break", "label": "Content"}, {"fieldname": "column_break_dbqu", "fieldtype": "Column Break"}, {"fieldname": "sender_id", "fieldtype": "Data", "hidden": 1, "label": "Sender Id"}, {"fieldname": "timestamp", "fieldtype": "Datetime", "label": "Delivered Time"}, {"fieldname": "status", "fieldtype": "Data", "label": "Status"}, {"fieldname": "receiver", "fieldtype": "Data", "hidden": 1, "label": "Receiver"}, {"fieldname": "synk_id", "fieldtype": "Data", "label": "Synk ID"}, {"fieldname": "social_media_channel", "fieldtype": "Data", "label": "Social Media Channel"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-30 10:23:00.566586", "modified_by": "Administrator", "module": "AI Agent", "name": "Message", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}