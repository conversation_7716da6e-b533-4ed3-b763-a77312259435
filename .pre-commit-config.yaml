exclude: 'node_modules|.git'
default_stages: [pre-commit]
fail_fast: false


repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0
    hooks:
      - id: trailing-whitespace
        files: "ai_agent.*"
        exclude: ".*json$|.*txt$|.*csv|.*md|.*svg"
      - id: check-merge-conflict
      - id: check-ast
      - id: check-json
      - id: check-toml
      - id: check-yaml
      - id: debug-statements

  - repo: https://github.com/astral-sh/ruff-pre-commit
    rev: v0.8.1
    hooks:
      - id: ruff
        name: "Run ruff import sorter"
        args: ["--select=I", "--fix"]

      - id: ruff
        name: "Run ruff linter"

      - id: ruff-format
        name: "Run ruff formatter"

  - repo: https://github.com/pre-commit/mirrors-prettier
    rev: v2.7.1
    hooks:
      - id: prettier
        types_or: [javascript, vue, scss]
        # Ignore any files that might contain jinja / bundles
        exclude: |
            (?x)^(
                ai_agent/public/dist/.*|
                .*node_modules.*|
                .*boilerplate.*|
                ai_agent/templates/includes/.*|
                ai_agent/public/js/lib/.*
            )$


  - repo: https://github.com/pre-commit/mirrors-eslint
    rev: v8.44.0
    hooks:
      - id: eslint
        types_or: [javascript]
        args: ['--quiet']
        # Ignore any files that might contain jinja / bundles
        exclude: |
            (?x)^(
                ai_agent/public/dist/.*|
                cypress/.*|
                .*node_modules.*|
                .*boilerplate.*|
                ai_agent/templates/includes/.*|
                ai_agent/public/js/lib/.*
            )$

ci:
    autoupdate_schedule: weekly
    skip: []
    submodules: false
