{"actions": [], "allow_rename": 1, "autoname": "CONVO-.#####", "creation": "2025-06-23 14:39:21.388286", "doctype": "DocType", "engine": "InnoDB", "field_order": ["ai_agent", "text", "audio_url", "vue_component"], "fields": [{"fieldname": "ai_agent", "fieldtype": "Link", "in_list_view": 1, "label": "AI Agent", "options": "AI Agent", "reqd": 1}, {"fieldname": "text", "fieldtype": "Text", "label": "Text"}, {"fieldname": "audio_url", "fieldtype": "Data", "label": "Audio URL"}, {"fieldname": "vue_component", "fieldtype": "HTML", "label": "Vue Component", "options": "<script type=\"module\" src=\"/assets/ai_agent/vue_widget/subtitle-wave-player.js\"></script>\n\n<subtitle-wave-player style=\"border-radius: 8px; overflow: hidden;\"></subtitle-wave-player>"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-07-16 12:49:11.584565", "modified_by": "Administrator", "module": "AI Agent", "name": "Conversation", "naming_rule": "Expression (old style)", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "creation", "sort_order": "DESC", "states": []}