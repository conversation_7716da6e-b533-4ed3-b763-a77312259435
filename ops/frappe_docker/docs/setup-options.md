# Containerized Production Setup

Make sure you've cloned this repository and switch to the directory before executing following commands.

Commands will generate YAML as per the environment for setup.

## Prerequisites

- [docker](https://docker.com/get-started)
- [docker compose v2](https://docs.docker.com/compose/cli-command)

## Setup Environment Variables

Copy the example docker environment file to `.env`:

```sh
cp example.env .env
```

Note: To know more about environment variable [read here](./environment-variables.md). Set the necessary variables in the `.env` file.

## Generate docker-compose.yml for variety of setups

Notes:

- Make sure to replace `<project-name>` with the desired name you wish to set for the project.
- This setup is not to be used for development. A complete development environment is available [here](../development)

### Store the yaml files

YAML files generated by `docker compose config` command can be stored in a directory. We will create a directory called `gitops` in the user's home.

```shell
mkdir ~/gitops
```

You can make the directory into a private git repo which stores the yaml and secrets. It can help in tracking changes.

Instead of `docker compose config`, you can directly use `docker compose up` to start the containers and skip storing the yamls in `gitops` directory.

### Setup Frappe without proxy and external MariaDB and Redis

In this case make sure you've set `DB_HOST`, `DB_PORT`, `REDIS_CACHE` and `REDIS_QUEUE` environment variables or the `configurator` will fail.

```sh
# Generate YAML
docker compose -f compose.yaml -f overrides/compose.noproxy.yaml config > ~/gitops/docker-compose.yml

# Start containers
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml up -d
```

### Setup ERPNext with proxy and external MariaDB and Redis

In this case make sure you've set `DB_HOST`, `DB_PORT`, `REDIS_CACHE` and `REDIS_QUEUE` environment variables or the `configurator` will fail.

```sh
# Generate YAML
docker compose -f compose.yaml \
  -f overrides/compose.proxy.yaml \
  config > ~/gitops/docker-compose.yml

# Start containers
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml up -d
```

### Setup Frappe using containerized MariaDB and Redis with Letsencrypt certificates.

In this case make sure you've set `LETSENCRYPT_EMAIL` and `SITES` environment variables are set or certificates won't work.

```sh
# Generate YAML
docker compose -f compose.yaml \
  -f overrides/compose.mariadb.yaml \
  -f overrides/compose.redis.yaml \
  -f overrides/compose.https.yaml \
  config > ~/gitops/docker-compose.yml

# Start containers
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml up -d
```

### Setup ERPNext using containerized MariaDB and Redis with Letsencrypt certificates.

In this case make sure you've set `LETSENCRYPT_EMAIL` and `SITES` environment variables are set or certificates won't work.

```sh
# Generate YAML
docker compose -f compose.yaml \
  -f overrides/compose.mariadb.yaml \
  -f overrides/compose.redis.yaml \
  -f overrides/compose.https.yaml \
  config > ~/gitops/docker-compose.yml

# Start containers
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml up -d
```

## Create first site

After starting containers, the first site needs to be created. Refer [site operations](./site-operations.md#setup-new-site).

## Updating Images

Switch to the root of the `frappe_docker` directory before running the following commands:

```sh
# Update environment variables ERPNEXT_VERSION and FRAPPE_VERSION
nano .env

# Pull new images
docker compose -f compose.yaml \
  # ... your other overrides
  config > ~/gitops/docker-compose.yml

# Pull images
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml pull

# Stop containers
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml down

# Restart containers
docker compose --project-name <project-name> -f ~/gitops/docker-compose.yml up -d
```

Note:

- pull and stop container commands can be skipped if immutable image tags are used
- `docker compose up -d` will pull new immutable tags if not found.

To migrate sites refer [site operations](./site-operations.md#migrate-site)
