(function(){"use strict";/**
* @vue/shared v3.5.16
* (c) 2018-present <PERSON><PERSON> (<PERSON>) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Rs(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const J={},fe=[],It=()=>{},Cr=()=>!1,qe=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Ps=e=>e.startsWith("onUpdate:"),rt=Object.assign,Ms=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},wr=Object.prototype.hasOwnProperty,K=(e,t)=>wr.call(e,t),W=Array.isArray,de=e=>Ge(e)==="[object Map]",xi=e=>Ge(e)==="[object Set]",H=e=>typeof e=="function",it=e=>typeof e=="string",Jt=e=>typeof e=="symbol",et=e=>e!==null&&typeof e=="object",Ci=e=>(et(e)||H(e))&&H(e.then)&&H(e.catch),wi=Object.prototype.toString,Ge=e=>wi.call(e),Sr=e=>Ge(e).slice(8,-1),Ye=e=>Ge(e)==="[object Object]",Os=e=>it(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,we=Rs(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Xe=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},Er=/-(\w)/g,mt=Xe(e=>e.replace(Er,(t,s)=>s?s.toUpperCase():"")),Tr=/\B([A-Z])/g,Pt=Xe(e=>e.replace(Tr,"-$1").toLowerCase()),Je=Xe(e=>e.charAt(0).toUpperCase()+e.slice(1)),As=Xe(e=>e?`on${Je(e)}`:""),Zt=(e,t)=>!Object.is(e,t),Ds=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},Si=(e,t,s,i=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:i,value:s})},Rr=e=>{const t=parseFloat(e);return isNaN(t)?e:t},Ei=e=>{const t=it(e)?Number(e):NaN;return isNaN(t)?e:t};let Ti;const Ze=()=>Ti||(Ti=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Qe(e){if(W(e)){const t={};for(let s=0;s<e.length;s++){const i=e[s],n=it(i)?Ar(i):Qe(i);if(n)for(const r in n)t[r]=n[r]}return t}else if(it(e)||et(e))return e}const Pr=/;(?![^(]*\))/g,Mr=/:([^]+)/,Or=/\/\*[^]*?\*\//g;function Ar(e){const t={};return e.replace(Or,"").split(Pr).forEach(s=>{if(s){const i=s.split(Mr);i.length>1&&(t[i[0].trim()]=i[1].trim())}}),t}function Se(e){let t="";if(it(e))t=e;else if(W(e))for(let s=0;s<e.length;s++){const i=Se(e[s]);i&&(t+=i+" ")}else if(et(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const Dr=Rs("itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly");function Ri(e){return!!e||e===""}const Pi=e=>!!(e&&e.__v_isRef===!0),re=e=>it(e)?e:e==null?"":W(e)||et(e)&&(e.toString===wi||!H(e.toString))?Pi(e)?re(e.value):JSON.stringify(e,Mi,2):String(e),Mi=(e,t)=>Pi(t)?Mi(e,t.value):de(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[i,n],r)=>(s[Ls(i,r)+" =>"]=n,s),{})}:xi(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>Ls(s))}:Jt(t)?Ls(t):et(t)&&!W(t)&&!Ye(t)?String(t):t,Ls=(e,t="")=>{var s;return Jt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let yt;class Lr{constructor(t=!1){this.detached=t,this._active=!0,this._on=0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=yt,!t&&yt&&(this.index=(yt.scopes||(yt.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=yt;try{return yt=this,t()}finally{yt=s}}}on(){++this._on===1&&(this.prevScope=yt,yt=this)}off(){this._on>0&&--this._on===0&&(yt=this.prevScope,this.prevScope=void 0)}stop(t){if(this._active){this._active=!1;let s,i;for(s=0,i=this.effects.length;s<i;s++)this.effects[s].stop();for(this.effects.length=0,s=0,i=this.cleanups.length;s<i;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,i=this.scopes.length;s<i;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function Ir(){return yt}let Z;const Is=new WeakSet;class Oi{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,yt&&yt.active&&yt.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,Is.has(this)&&(Is.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||Di(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Ni(this),Li(this);const t=Z,s=Dt;Z=this,Dt=!0;try{return this.fn()}finally{Ii(this),Z=t,Dt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Ws(t);this.deps=this.depsTail=void 0,Ni(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?Is.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){Ns(this)&&this.run()}get dirty(){return Ns(this)}}let Ai=0,Ee,Te;function Di(e,t=!1){if(e.flags|=8,t){e.next=Te,Te=e;return}e.next=Ee,Ee=e}function $s(){Ai++}function ks(){if(--Ai>0)return;if(Te){let t=Te;for(Te=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;Ee;){let t=Ee;for(Ee=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(i){e||(e=i)}t=s}}if(e)throw e}function Li(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function Ii(e){let t,s=e.depsTail,i=s;for(;i;){const n=i.prevDep;i.version===-1?(i===s&&(s=n),Ws(i),$r(i)):t=i,i.dep.activeLink=i.prevActiveLink,i.prevActiveLink=void 0,i=n}e.deps=t,e.depsTail=s}function Ns(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&($i(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function $i(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===Re)||(e.globalVersion=Re,!e.isSSR&&e.flags&128&&(!e.deps&&!e._dirty||!Ns(e))))return;e.flags|=2;const t=e.dep,s=Z,i=Dt;Z=e,Dt=!0;try{Li(e);const n=e.fn(e._value);(t.version===0||Zt(n,e._value))&&(e.flags|=128,e._value=n,t.version++)}catch(n){throw t.version++,n}finally{Z=s,Dt=i,Ii(e),e.flags&=-3}}function Ws(e,t=!1){const{dep:s,prevSub:i,nextSub:n}=e;if(i&&(i.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=i,e.nextSub=void 0),s.subs===e&&(s.subs=i,!i&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)Ws(r,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function $r(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let Dt=!0;const ki=[];function $t(){ki.push(Dt),Dt=!1}function kt(){const e=ki.pop();Dt=e===void 0?!0:e}function Ni(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=Z;Z=void 0;try{t()}finally{Z=s}}}let Re=0;class kr{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class Fs{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Z||!Dt||Z===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==Z)s=this.activeLink=new kr(Z,this),Z.deps?(s.prevDep=Z.depsTail,Z.depsTail.nextDep=s,Z.depsTail=s):Z.deps=Z.depsTail=s,Wi(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const i=s.nextDep;i.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=i),s.prevDep=Z.depsTail,s.nextDep=void 0,Z.depsTail.nextDep=s,Z.depsTail=s,Z.deps===s&&(Z.deps=i)}return s}trigger(t){this.version++,Re++,this.notify(t)}notify(t){$s();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{ks()}}}function Wi(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let i=t.deps;i;i=i.nextDep)Wi(i)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const js=new WeakMap,oe=Symbol(""),Hs=Symbol(""),Pe=Symbol("");function ft(e,t,s){if(Dt&&Z){let i=js.get(e);i||js.set(e,i=new Map);let n=i.get(s);n||(i.set(s,n=new Fs),n.map=i,n.key=s),n.track()}}function Ut(e,t,s,i,n,r){const o=js.get(e);if(!o){Re++;return}const l=a=>{a&&a.trigger()};if($s(),t==="clear")o.forEach(l);else{const a=W(e),f=a&&Os(s);if(a&&s==="length"){const c=Number(i);o.forEach((h,p)=>{(p==="length"||p===Pe||!Jt(p)&&p>=c)&&l(h)})}else switch((s!==void 0||o.has(void 0))&&l(o.get(s)),f&&l(o.get(Pe)),t){case"add":a?f&&l(o.get("length")):(l(o.get(oe)),de(e)&&l(o.get(Hs)));break;case"delete":a||(l(o.get(oe)),de(e)&&l(o.get(Hs)));break;case"set":de(e)&&l(o.get(oe));break}}ks()}function he(e){const t=U(e);return t===e?t:(ft(t,"iterate",Pe),Mt(e)?t:t.map(at))}function ts(e){return ft(e=U(e),"iterate",Pe),e}const Nr={__proto__:null,[Symbol.iterator](){return Bs(this,Symbol.iterator,at)},concat(...e){return he(this).concat(...e.map(t=>W(t)?he(t):t))},entries(){return Bs(this,"entries",e=>(e[1]=at(e[1]),e))},every(e,t){return Kt(this,"every",e,t,void 0,arguments)},filter(e,t){return Kt(this,"filter",e,t,s=>s.map(at),arguments)},find(e,t){return Kt(this,"find",e,t,at,arguments)},findIndex(e,t){return Kt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Kt(this,"findLast",e,t,at,arguments)},findLastIndex(e,t){return Kt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Kt(this,"forEach",e,t,void 0,arguments)},includes(...e){return zs(this,"includes",e)},indexOf(...e){return zs(this,"indexOf",e)},join(e){return he(this).join(e)},lastIndexOf(...e){return zs(this,"lastIndexOf",e)},map(e,t){return Kt(this,"map",e,t,void 0,arguments)},pop(){return Me(this,"pop")},push(...e){return Me(this,"push",e)},reduce(e,...t){return Fi(this,"reduce",e,t)},reduceRight(e,...t){return Fi(this,"reduceRight",e,t)},shift(){return Me(this,"shift")},some(e,t){return Kt(this,"some",e,t,void 0,arguments)},splice(...e){return Me(this,"splice",e)},toReversed(){return he(this).toReversed()},toSorted(e){return he(this).toSorted(e)},toSpliced(...e){return he(this).toSpliced(...e)},unshift(...e){return Me(this,"unshift",e)},values(){return Bs(this,"values",at)}};function Bs(e,t,s){const i=ts(e),n=i[t]();return i!==e&&!Mt(e)&&(n._next=n.next,n.next=()=>{const r=n._next();return r.value&&(r.value=s(r.value)),r}),n}const Wr=Array.prototype;function Kt(e,t,s,i,n,r){const o=ts(e),l=o!==e&&!Mt(e),a=o[t];if(a!==Wr[t]){const h=a.apply(e,r);return l?at(h):h}let f=s;o!==e&&(l?f=function(h,p){return s.call(this,at(h),p,e)}:s.length>2&&(f=function(h,p){return s.call(this,h,p,e)}));const c=a.call(o,f,i);return l&&n?n(c):c}function Fi(e,t,s,i){const n=ts(e);let r=s;return n!==e&&(Mt(e)?s.length>3&&(r=function(o,l,a){return s.call(this,o,l,a,e)}):r=function(o,l,a){return s.call(this,o,at(l),a,e)}),n[t](r,...i)}function zs(e,t,s){const i=U(e);ft(i,"iterate",Pe);const n=i[t](...s);return(n===-1||n===!1)&&Ks(s[0])?(s[0]=U(s[0]),i[t](...s)):n}function Me(e,t,s=[]){$t(),$s();const i=U(e)[t].apply(e,s);return ks(),kt(),i}const Fr=Rs("__proto__,__v_isRef,__isVue"),ji=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Jt));function jr(e){Jt(e)||(e=String(e));const t=U(this);return ft(t,"has",e),t.hasOwnProperty(e)}class Hi{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,i){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return r;if(s==="__v_raw")return i===(n?r?qi:Ki:r?Ui:Vi).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(i)?t:void 0;const o=W(t);if(!n){let a;if(o&&(a=Nr[s]))return a;if(s==="hasOwnProperty")return jr}const l=Reflect.get(t,s,ct(t)?t:i);return(Jt(s)?ji.has(s):Fr(s))||(n||ft(t,"get",s),r)?l:ct(l)?o&&Os(s)?l:l.value:et(l)?n?Gi(l):Us(l):l}}class Bi extends Hi{constructor(t=!1){super(!1,t)}set(t,s,i,n){let r=t[s];if(!this._isShallow){const a=Qt(r);if(!Mt(i)&&!Qt(i)&&(r=U(r),i=U(i)),!W(t)&&ct(r)&&!ct(i))return a?!1:(r.value=i,!0)}const o=W(t)&&Os(s)?Number(s)<t.length:K(t,s),l=Reflect.set(t,s,i,ct(t)?t:n);return t===U(n)&&(o?Zt(i,r)&&Ut(t,"set",s,i):Ut(t,"add",s,i)),l}deleteProperty(t,s){const i=K(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&i&&Ut(t,"delete",s,void 0),n}has(t,s){const i=Reflect.has(t,s);return(!Jt(s)||!ji.has(s))&&ft(t,"has",s),i}ownKeys(t){return ft(t,"iterate",W(t)?"length":oe),Reflect.ownKeys(t)}}class zi extends Hi{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Hr=new Bi,Br=new zi,zr=new Bi(!0),Vr=new zi(!0),Vs=e=>e,es=e=>Reflect.getPrototypeOf(e);function Ur(e,t,s){return function(...i){const n=this.__v_raw,r=U(n),o=de(r),l=e==="entries"||e===Symbol.iterator&&o,a=e==="keys"&&o,f=n[e](...i),c=s?Vs:t?rs:at;return!t&&ft(r,"iterate",a?Hs:oe),{next(){const{value:h,done:p}=f.next();return p?{value:h,done:p}:{value:l?[c(h[0]),c(h[1])]:c(h),done:p}},[Symbol.iterator](){return this}}}}function ss(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Kr(e,t){const s={get(n){const r=this.__v_raw,o=U(r),l=U(n);e||(Zt(n,l)&&ft(o,"get",n),ft(o,"get",l));const{has:a}=es(o),f=t?Vs:e?rs:at;if(a.call(o,n))return f(r.get(n));if(a.call(o,l))return f(r.get(l));r!==o&&r.get(n)},get size(){const n=this.__v_raw;return!e&&ft(U(n),"iterate",oe),Reflect.get(n,"size",n)},has(n){const r=this.__v_raw,o=U(r),l=U(n);return e||(Zt(n,l)&&ft(o,"has",n),ft(o,"has",l)),n===l?r.has(n):r.has(n)||r.has(l)},forEach(n,r){const o=this,l=o.__v_raw,a=U(l),f=t?Vs:e?rs:at;return!e&&ft(a,"iterate",oe),l.forEach((c,h)=>n.call(r,f(c),f(h),o))}};return rt(s,e?{add:ss("add"),set:ss("set"),delete:ss("delete"),clear:ss("clear")}:{add(n){!t&&!Mt(n)&&!Qt(n)&&(n=U(n));const r=U(this);return es(r).has.call(r,n)||(r.add(n),Ut(r,"add",n,n)),this},set(n,r){!t&&!Mt(r)&&!Qt(r)&&(r=U(r));const o=U(this),{has:l,get:a}=es(o);let f=l.call(o,n);f||(n=U(n),f=l.call(o,n));const c=a.call(o,n);return o.set(n,r),f?Zt(r,c)&&Ut(o,"set",n,r):Ut(o,"add",n,r),this},delete(n){const r=U(this),{has:o,get:l}=es(r);let a=o.call(r,n);a||(n=U(n),a=o.call(r,n)),l&&l.call(r,n);const f=r.delete(n);return a&&Ut(r,"delete",n,void 0),f},clear(){const n=U(this),r=n.size!==0,o=n.clear();return r&&Ut(n,"clear",void 0,void 0),o}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=Ur(n,e,t)}),s}function is(e,t){const s=Kr(e,t);return(i,n,r)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?i:Reflect.get(K(s,n)&&n in i?s:i,n,r)}const qr={get:is(!1,!1)},Gr={get:is(!1,!0)},Yr={get:is(!0,!1)},Xr={get:is(!0,!0)},Vi=new WeakMap,Ui=new WeakMap,Ki=new WeakMap,qi=new WeakMap;function Jr(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Zr(e){return e.__v_skip||!Object.isExtensible(e)?0:Jr(Sr(e))}function Us(e){return Qt(e)?e:ns(e,!1,Hr,qr,Vi)}function Qr(e){return ns(e,!1,zr,Gr,Ui)}function Gi(e){return ns(e,!0,Br,Yr,Ki)}function xa(e){return ns(e,!0,Vr,Xr,qi)}function ns(e,t,s,i,n){if(!et(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=Zr(e);if(r===0)return e;const o=n.get(e);if(o)return o;const l=new Proxy(e,r===2?i:s);return n.set(e,l),l}function pe(e){return Qt(e)?pe(e.__v_raw):!!(e&&e.__v_isReactive)}function Qt(e){return!!(e&&e.__v_isReadonly)}function Mt(e){return!!(e&&e.__v_isShallow)}function Ks(e){return e?!!e.__v_raw:!1}function U(e){const t=e&&e.__v_raw;return t?U(t):e}function to(e){return!K(e,"__v_skip")&&Object.isExtensible(e)&&Si(e,"__v_skip",!0),e}const at=e=>et(e)?Us(e):e,rs=e=>et(e)?Gi(e):e;function ct(e){return e?e.__v_isRef===!0:!1}function st(e){return eo(e,!1)}function eo(e,t){return ct(e)?e:new so(e,t)}class so{constructor(t,s){this.dep=new Fs,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:U(t),this._value=s?t:at(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,i=this.__v_isShallow||Mt(t)||Qt(t);t=i?t:U(t),Zt(t,s)&&(this._rawValue=t,this._value=i?t:at(t),this.dep.trigger())}}function Yi(e){return ct(e)?e.value:e}const io={get:(e,t,s)=>t==="__v_raw"?e:Yi(Reflect.get(e,t,s)),set:(e,t,s,i)=>{const n=e[t];return ct(n)&&!ct(s)?(n.value=s,!0):Reflect.set(e,t,s,i)}};function Xi(e){return pe(e)?e:new Proxy(e,io)}class no{constructor(t,s,i){this.fn=t,this.setter=s,this._value=void 0,this.dep=new Fs(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=Re-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=i}notify(){if(this.flags|=16,!(this.flags&8)&&Z!==this)return Di(this,!0),!0}get value(){const t=this.dep.track();return $i(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function ro(e,t,s=!1){let i,n;return H(e)?i=e:(i=e.get,n=e.set),new no(i,n,s)}const os={},ls=new WeakMap;let le;function oo(e,t=!1,s=le){if(s){let i=ls.get(s);i||ls.set(s,i=[]),i.push(e)}}function lo(e,t,s=J){const{immediate:i,deep:n,once:r,scheduler:o,augmentJob:l,call:a}=s,f=C=>n?C:Mt(C)||n===!1||n===0?qt(C,1):qt(C);let c,h,p,m,M=!1,D=!1;if(ct(e)?(h=()=>e.value,M=Mt(e)):pe(e)?(h=()=>f(e),M=!0):W(e)?(D=!0,M=e.some(C=>pe(C)||Mt(C)),h=()=>e.map(C=>{if(ct(C))return C.value;if(pe(C))return f(C);if(H(C))return a?a(C,2):C()})):H(e)?t?h=a?()=>a(e,2):e:h=()=>{if(p){$t();try{p()}finally{kt()}}const C=le;le=c;try{return a?a(e,3,[m]):e(m)}finally{le=C}}:h=It,t&&n){const C=h,A=n===!0?1/0:n;h=()=>qt(C(),A)}const N=Ir(),T=()=>{c.stop(),N&&N.active&&Ms(N.effects,c)};if(r&&t){const C=t;t=(...A)=>{C(...A),T()}}let O=D?new Array(e.length).fill(os):os;const L=C=>{if(!(!(c.flags&1)||!c.dirty&&!C))if(t){const A=c.run();if(n||M||(D?A.some((F,B)=>Zt(F,O[B])):Zt(A,O))){p&&p();const F=le;le=c;try{const B=[A,O===os?void 0:D&&O[0]===os?[]:O,m];O=A,a?a(t,3,B):t(...B)}finally{le=F}}}else c.run()};return l&&l(L),c=new Oi(h),c.scheduler=o?()=>o(L,!1):L,m=C=>oo(C,!1,c),p=c.onStop=()=>{const C=ls.get(c);if(C){if(a)a(C,4);else for(const A of C)A();ls.delete(c)}},t?i?L(!0):O=c.run():o?o(L.bind(null,!0),!0):c.run(),T.pause=c.pause.bind(c),T.resume=c.resume.bind(c),T.stop=T,T}function qt(e,t=1/0,s){if(t<=0||!et(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,ct(e))qt(e.value,t,s);else if(W(e))for(let i=0;i<e.length;i++)qt(e[i],t,s);else if(xi(e)||de(e))e.forEach(i=>{qt(i,t,s)});else if(Ye(e)){for(const i in e)qt(e[i],t,s);for(const i of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,i)&&qt(e[i],t,s)}return e}/**
* @vue/runtime-core v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/const Oe=[];let qs=!1;function Ca(e,...t){if(qs)return;qs=!0,$t();const s=Oe.length?Oe[Oe.length-1].component:null,i=s&&s.appContext.config.warnHandler,n=ao();if(i)me(i,s,11,[e+t.map(r=>{var o,l;return(l=(o=r.toString)==null?void 0:o.call(r))!=null?l:JSON.stringify(r)}).join(""),s&&s.proxy,n.map(({vnode:r})=>`at <${Gn(s,r.type)}>`).join(`
`),n]);else{const r=[`[Vue warn]: ${e}`,...t];n.length&&r.push(`
`,...co(n)),console.warn(...r)}kt(),qs=!1}function ao(){let e=Oe[Oe.length-1];if(!e)return[];const t=[];for(;e;){const s=t[0];s&&s.vnode===e?s.recurseCount++:t.push({vnode:e,recurseCount:0});const i=e.component&&e.component.parent;e=i&&i.vnode}return t}function co(e){const t=[];return e.forEach((s,i)=>{t.push(...i===0?[]:[`
`],...uo(s))}),t}function uo({vnode:e,recurseCount:t}){const s=t>0?`... (${t} recursive calls)`:"",i=e.component?e.component.parent==null:!1,n=` at <${Gn(e.component,e.type,i)}`,r=">"+s;return e.props?[n,...fo(e.props),r]:[n+r]}function fo(e){const t=[],s=Object.keys(e);return s.slice(0,3).forEach(i=>{t.push(...Ji(i,e[i]))}),s.length>3&&t.push(" ..."),t}function Ji(e,t,s){return it(t)?(t=JSON.stringify(t),s?t:[`${e}=${t}`]):typeof t=="number"||typeof t=="boolean"||t==null?s?t:[`${e}=${t}`]:ct(t)?(t=Ji(e,U(t.value),!0),s?t:[`${e}=Ref<`,t,">"]):H(t)?[`${e}=fn${t.name?`<${t.name}>`:""}`]:(t=U(t),s?t:[`${e}=`,t])}function me(e,t,s,i){try{return i?e(...i):e()}catch(n){as(n,t,s)}}function Nt(e,t,s,i){if(H(e)){const n=me(e,t,s,i);return n&&Ci(n)&&n.catch(r=>{as(r,t,s)}),n}if(W(e)){const n=[];for(let r=0;r<e.length;r++)n.push(Nt(e[r],t,s,i));return n}}function as(e,t,s,i=!0){const n=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:o}=t&&t.appContext.config||J;if(t){let l=t.parent;const a=t.proxy,f=`https://vuejs.org/error-reference/#runtime-${s}`;for(;l;){const c=l.ec;if(c){for(let h=0;h<c.length;h++)if(c[h](e,a,f)===!1)return}l=l.parent}if(r){$t(),me(r,null,10,[e,a,f]),kt();return}}ho(e,s,n,i,o)}function ho(e,t,s,i=!0,n=!1){if(n)throw e;console.error(e)}const gt=[];let Wt=-1;const ge=[];let te=null,ve=0;const Zi=Promise.resolve();let cs=null;function Gs(e){const t=cs||Zi;return e?t.then(this?e.bind(this):e):t}function po(e){let t=Wt+1,s=gt.length;for(;t<s;){const i=t+s>>>1,n=gt[i],r=Ae(n);r<e||r===e&&n.flags&2?t=i+1:s=i}return t}function Ys(e){if(!(e.flags&1)){const t=Ae(e),s=gt[gt.length-1];!s||!(e.flags&2)&&t>=Ae(s)?gt.push(e):gt.splice(po(t),0,e),e.flags|=1,Qi()}}function Qi(){cs||(cs=Zi.then(sn))}function mo(e){W(e)?ge.push(...e):te&&e.id===-1?te.splice(ve+1,0,e):e.flags&1||(ge.push(e),e.flags|=1),Qi()}function tn(e,t,s=Wt+1){for(;s<gt.length;s++){const i=gt[s];if(i&&i.flags&2){if(e&&i.id!==e.uid)continue;gt.splice(s,1),s--,i.flags&4&&(i.flags&=-2),i(),i.flags&4||(i.flags&=-2)}}}function en(e){if(ge.length){const t=[...new Set(ge)].sort((s,i)=>Ae(s)-Ae(i));if(ge.length=0,te){te.push(...t);return}for(te=t,ve=0;ve<te.length;ve++){const s=te[ve];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}te=null,ve=0}}const Ae=e=>e.id==null?e.flags&2?-1:1/0:e.id;function sn(e){try{for(Wt=0;Wt<gt.length;Wt++){const t=gt[Wt];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),me(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;Wt<gt.length;Wt++){const t=gt[Wt];t&&(t.flags&=-2)}Wt=-1,gt.length=0,en(),cs=null,(gt.length||ge.length)&&sn()}}let Ct=null,nn=null;function us(e){const t=Ct;return Ct=e,nn=e&&e.type.__scopeId||null,t}function G(e,t=Ct,s){if(!t||e._n)return e;const i=(...n)=>{i._d&&Wn(-1);const r=us(t);let o;try{o=e(...n)}finally{us(r),i._d&&Wn(1)}return o};return i._n=!0,i._c=!0,i._d=!0,i}function rn(e,t){if(Ct===null)return e;const s=ys(Ct),i=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[r,o,l,a=J]=t[n];r&&(H(r)&&(r={mounted:r,updated:r}),r.deep&&qt(o),i.push({dir:r,instance:s,value:o,oldValue:void 0,arg:l,modifiers:a}))}return e}function ae(e,t,s,i){const n=e.dirs,r=t&&t.dirs;for(let o=0;o<n.length;o++){const l=n[o];r&&(l.oldValue=r[o].value);let a=l.dir[i];a&&($t(),Nt(a,s,8,[e.el,l,e,t]),kt())}}const go=Symbol("_vte"),vo=e=>e.__isTeleport;function Xs(e,t){e.shapeFlag&6&&e.component?(e.transition=t,Xs(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function fs(e,t){return H(e)?rt({name:e.name},t,{setup:e}):e}function on(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function ds(e,t,s,i,n=!1){if(W(e)){e.forEach((M,D)=>ds(M,t&&(W(t)?t[D]:t),s,i,n));return}if(De(i)&&!n){i.shapeFlag&512&&i.type.__asyncResolved&&i.component.subTree.component&&ds(e,t,s,i.component.subTree);return}const r=i.shapeFlag&4?ys(i.component):i.el,o=n?null:r,{i:l,r:a}=e,f=t&&t.r,c=l.refs===J?l.refs={}:l.refs,h=l.setupState,p=U(h),m=h===J?()=>!1:M=>K(p,M);if(f!=null&&f!==a&&(it(f)?(c[f]=null,m(f)&&(h[f]=null)):ct(f)&&(f.value=null)),H(a))me(a,l,12,[o,c]);else{const M=it(a),D=ct(a);if(M||D){const N=()=>{if(e.f){const T=M?m(a)?h[a]:c[a]:a.value;n?W(T)&&Ms(T,r):W(T)?T.includes(r)||T.push(r):M?(c[a]=[r],m(a)&&(h[a]=c[a])):(a.value=[r],e.k&&(c[e.k]=a.value))}else M?(c[a]=o,m(a)&&(h[a]=o)):D&&(a.value=o,e.k&&(c[e.k]=o))};o?(N.id=-1,wt(N,s)):N()}}}Ze().requestIdleCallback,Ze().cancelIdleCallback;const De=e=>!!e.type.__asyncLoader,ln=e=>e.type.__isKeepAlive;function _o(e,t){an(e,"a",t)}function bo(e,t){an(e,"da",t)}function an(e,t,s=ht){const i=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(hs(t,i,s),s){let n=s.parent;for(;n&&n.parent;)ln(n.parent.vnode)&&yo(i,t,s,n),n=n.parent}}function yo(e,t,s,i){const n=hs(t,e,i,!0);Js(()=>{Ms(i[t],n)},s)}function hs(e,t,s=ht,i=!1){if(s){const n=s[e]||(s[e]=[]),r=t.__weh||(t.__weh=(...o)=>{$t();const l=We(s),a=Nt(t,s,e,o);return l(),kt(),a});return i?n.unshift(r):n.push(r),r}}const Gt=e=>(t,s=ht)=>{(!Fe||e==="sp")&&hs(e,(...i)=>t(...i),s)},xo=Gt("bm"),cn=Gt("m"),Co=Gt("bu"),wo=Gt("u"),So=Gt("bum"),Js=Gt("um"),Eo=Gt("sp"),To=Gt("rtg"),Ro=Gt("rtc");function Po(e,t=ht){hs("ec",e,t)}const un="components",Mo="directives";function q(e,t){return fn(un,e,!0,t)||e}const Oo=Symbol.for("v-ndc");function Ao(e){return fn(Mo,e)}function fn(e,t,s=!0,i=!1){const n=Ct||ht;if(n){const r=n.type;if(e===un){const l=qn(r,!1);if(l&&(l===t||l===mt(t)||l===Je(mt(t))))return r}const o=dn(n[e]||r[e],t)||dn(n.appContext[e],t);return!o&&i?r:o}}function dn(e,t){return e&&(e[t]||e[mt(t)]||e[Je(mt(t))])}function Zs(e,t,s,i){let n;const r=s,o=W(e);if(o||it(e)){const l=o&&pe(e);let a=!1,f=!1;l&&(a=!Mt(e),f=Qt(e),e=ts(e)),n=new Array(e.length);for(let c=0,h=e.length;c<h;c++)n[c]=t(a?f?rs(at(e[c])):at(e[c]):e[c],c,void 0,r)}else if(typeof e=="number"){n=new Array(e);for(let l=0;l<e;l++)n[l]=t(l+1,l,void 0,r)}else if(et(e))if(e[Symbol.iterator])n=Array.from(e,(l,a)=>t(l,a,void 0,r));else{const l=Object.keys(e);n=new Array(l.length);for(let a=0,f=l.length;a<f;a++){const c=l[a];n[a]=t(e[c],c,a,r)}}else n=[];return n}const Qs=e=>e?Vn(e)?ys(e):Qs(e.parent):null,Le=rt(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Qs(e.parent),$root:e=>Qs(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>gn(e),$forceUpdate:e=>e.f||(e.f=()=>{Ys(e.update)}),$nextTick:e=>e.n||(e.n=Gs.bind(e.proxy)),$watch:e=>Qo.bind(e)}),ti=(e,t)=>e!==J&&!e.__isScriptSetup&&K(e,t),Do={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:i,data:n,props:r,accessCache:o,type:l,appContext:a}=e;let f;if(t[0]!=="$"){const m=o[t];if(m!==void 0)switch(m){case 1:return i[t];case 2:return n[t];case 4:return s[t];case 3:return r[t]}else{if(ti(i,t))return o[t]=1,i[t];if(n!==J&&K(n,t))return o[t]=2,n[t];if((f=e.propsOptions[0])&&K(f,t))return o[t]=3,r[t];if(s!==J&&K(s,t))return o[t]=4,s[t];ei&&(o[t]=0)}}const c=Le[t];let h,p;if(c)return t==="$attrs"&&ft(e.attrs,"get",""),c(e);if((h=l.__cssModules)&&(h=h[t]))return h;if(s!==J&&K(s,t))return o[t]=4,s[t];if(p=a.config.globalProperties,K(p,t))return p[t]},set({_:e},t,s){const{data:i,setupState:n,ctx:r}=e;return ti(n,t)?(n[t]=s,!0):i!==J&&K(i,t)?(i[t]=s,!0):K(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:i,appContext:n,propsOptions:r}},o){let l;return!!s[o]||e!==J&&K(e,o)||ti(t,o)||(l=r[0])&&K(l,o)||K(i,o)||K(Le,o)||K(n.config.globalProperties,o)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:K(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function hn(e){return W(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let ei=!0;function Lo(e){const t=gn(e),s=e.proxy,i=e.ctx;ei=!1,t.beforeCreate&&pn(t.beforeCreate,e,"bc");const{data:n,computed:r,methods:o,watch:l,provide:a,inject:f,created:c,beforeMount:h,mounted:p,beforeUpdate:m,updated:M,activated:D,deactivated:N,beforeDestroy:T,beforeUnmount:O,destroyed:L,unmounted:C,render:A,renderTracked:F,renderTriggered:B,errorCaptured:X,serverPrefetch:nt,expose:tt,inheritAttrs:_t,components:At,directives:Xt,filters:ne}=t;if(f&&Io(f,i,null),o)for(const v in o){const w=o[v];H(w)&&(i[v]=w.bind(s))}if(n){const v=n.call(s,s);et(v)&&(e.data=Us(v))}if(ei=!0,r)for(const v in r){const w=r[v],z=H(w)?w.bind(s,s):H(w.get)?w.get.bind(s,s):It,pt=!H(w)&&H(w.set)?w.set.bind(s):It,bt=Yn({get:z,set:pt});Object.defineProperty(i,v,{enumerable:!0,configurable:!0,get:()=>bt.value,set:lt=>bt.value=lt})}if(l)for(const v in l)mn(l[v],i,s,v);if(a){const v=H(a)?a.call(s):a;Reflect.ownKeys(v).forEach(w=>{jo(w,v[w])})}c&&pn(c,e,"c");function ot(v,w){W(w)?w.forEach(z=>v(z.bind(s))):w&&v(w.bind(s))}if(ot(xo,h),ot(cn,p),ot(Co,m),ot(wo,M),ot(_o,D),ot(bo,N),ot(Po,X),ot(Ro,F),ot(To,B),ot(So,O),ot(Js,C),ot(Eo,nt),W(tt))if(tt.length){const v=e.exposed||(e.exposed={});tt.forEach(w=>{Object.defineProperty(v,w,{get:()=>s[w],set:z=>s[w]=z})})}else e.exposed||(e.exposed={});A&&e.render===It&&(e.render=A),_t!=null&&(e.inheritAttrs=_t),At&&(e.components=At),Xt&&(e.directives=Xt),nt&&on(e)}function Io(e,t,s=It){W(e)&&(e=si(e));for(const i in e){const n=e[i];let r;et(n)?"default"in n?r=ms(n.from||i,n.default,!0):r=ms(n.from||i):r=ms(n),ct(r)?Object.defineProperty(t,i,{enumerable:!0,configurable:!0,get:()=>r.value,set:o=>r.value=o}):t[i]=r}}function pn(e,t,s){Nt(W(e)?e.map(i=>i.bind(t.proxy)):e.bind(t.proxy),t,s)}function mn(e,t,s,i){let n=i.includes(".")?Ln(s,i):()=>s[i];if(it(e)){const r=t[e];H(r)&&ee(n,r)}else if(H(e))ee(n,e.bind(s));else if(et(e))if(W(e))e.forEach(r=>mn(r,t,s,i));else{const r=H(e.handler)?e.handler.bind(s):t[e.handler];H(r)&&ee(n,r,e)}}function gn(e){const t=e.type,{mixins:s,extends:i}=t,{mixins:n,optionsCache:r,config:{optionMergeStrategies:o}}=e.appContext,l=r.get(t);let a;return l?a=l:!n.length&&!s&&!i?a=t:(a={},n.length&&n.forEach(f=>ps(a,f,o,!0)),ps(a,t,o)),et(t)&&r.set(t,a),a}function ps(e,t,s,i=!1){const{mixins:n,extends:r}=t;r&&ps(e,r,s,!0),n&&n.forEach(o=>ps(e,o,s,!0));for(const o in t)if(!(i&&o==="expose")){const l=$o[o]||s&&s[o];e[o]=l?l(e[o],t[o]):t[o]}return e}const $o={data:vn,props:_n,emits:_n,methods:Ie,computed:Ie,beforeCreate:vt,created:vt,beforeMount:vt,mounted:vt,beforeUpdate:vt,updated:vt,beforeDestroy:vt,beforeUnmount:vt,destroyed:vt,unmounted:vt,activated:vt,deactivated:vt,errorCaptured:vt,serverPrefetch:vt,components:Ie,directives:Ie,watch:No,provide:vn,inject:ko};function vn(e,t){return t?e?function(){return rt(H(e)?e.call(this,this):e,H(t)?t.call(this,this):t)}:t:e}function ko(e,t){return Ie(si(e),si(t))}function si(e){if(W(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function vt(e,t){return e?[...new Set([].concat(e,t))]:t}function Ie(e,t){return e?rt(Object.create(null),e,t):t}function _n(e,t){return e?W(e)&&W(t)?[...new Set([...e,...t])]:rt(Object.create(null),hn(e),hn(t??{})):t}function No(e,t){if(!e)return t;if(!t)return e;const s=rt(Object.create(null),e);for(const i in t)s[i]=vt(e[i],t[i]);return s}function bn(){return{app:null,config:{isNativeTag:Cr,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let Wo=0;function Fo(e,t){return function(i,n=null){H(i)||(i=rt({},i)),n!=null&&!et(n)&&(n=null);const r=bn(),o=new WeakSet,l=[];let a=!1;const f=r.app={_uid:Wo++,_component:i,_props:n,_container:null,_context:r,_instance:null,version:yl,get config(){return r.config},set config(c){},use(c,...h){return o.has(c)||(c&&H(c.install)?(o.add(c),c.install(f,...h)):H(c)&&(o.add(c),c(f,...h))),f},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),f},component(c,h){return h?(r.components[c]=h,f):r.components[c]},directive(c,h){return h?(r.directives[c]=h,f):r.directives[c]},mount(c,h,p){if(!a){const m=f._ceVNode||k(i,n);return m.appContext=r,p===!0?p="svg":p===!1&&(p=void 0),e(m,c,p),a=!0,f._container=c,c.__vue_app__=f,ys(m.component)}},onUnmount(c){l.push(c)},unmount(){a&&(Nt(l,f._instance,16),e(null,f._container),delete f._container.__vue_app__)},provide(c,h){return r.provides[c]=h,f},runWithContext(c){const h=_e;_e=f;try{return c()}finally{_e=h}}};return f}}let _e=null;function jo(e,t){if(ht){let s=ht.provides;const i=ht.parent&&ht.parent.provides;i===s&&(s=ht.provides=Object.create(i)),s[e]=t}}function ms(e,t,s=!1){const i=ht||Ct;if(i||_e){let n=_e?_e._context.provides:i?i.parent==null||i.ce?i.vnode.appContext&&i.vnode.appContext.provides:i.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&H(t)?t.call(i&&i.proxy):t}}const yn={},xn=()=>Object.create(yn),Cn=e=>Object.getPrototypeOf(e)===yn;function Ho(e,t,s,i=!1){const n={},r=xn();e.propsDefaults=Object.create(null),wn(e,t,n,r);for(const o in e.propsOptions[0])o in n||(n[o]=void 0);s?e.props=i?n:Qr(n):e.type.props?e.props=n:e.props=r,e.attrs=r}function Bo(e,t,s,i){const{props:n,attrs:r,vnode:{patchFlag:o}}=e,l=U(n),[a]=e.propsOptions;let f=!1;if((i||o>0)&&!(o&16)){if(o&8){const c=e.vnode.dynamicProps;for(let h=0;h<c.length;h++){let p=c[h];if(gs(e.emitsOptions,p))continue;const m=t[p];if(a)if(K(r,p))m!==r[p]&&(r[p]=m,f=!0);else{const M=mt(p);n[M]=ii(a,l,M,m,e,!1)}else m!==r[p]&&(r[p]=m,f=!0)}}}else{wn(e,t,n,r)&&(f=!0);let c;for(const h in l)(!t||!K(t,h)&&((c=Pt(h))===h||!K(t,c)))&&(a?s&&(s[h]!==void 0||s[c]!==void 0)&&(n[h]=ii(a,l,h,void 0,e,!0)):delete n[h]);if(r!==l)for(const h in r)(!t||!K(t,h))&&(delete r[h],f=!0)}f&&Ut(e.attrs,"set","")}function wn(e,t,s,i){const[n,r]=e.propsOptions;let o=!1,l;if(t)for(let a in t){if(we(a))continue;const f=t[a];let c;n&&K(n,c=mt(a))?!r||!r.includes(c)?s[c]=f:(l||(l={}))[c]=f:gs(e.emitsOptions,a)||(!(a in i)||f!==i[a])&&(i[a]=f,o=!0)}if(r){const a=U(s),f=l||J;for(let c=0;c<r.length;c++){const h=r[c];s[h]=ii(n,a,h,f[h],e,!K(f,h))}}return o}function ii(e,t,s,i,n,r){const o=e[s];if(o!=null){const l=K(o,"default");if(l&&i===void 0){const a=o.default;if(o.type!==Function&&!o.skipFactory&&H(a)){const{propsDefaults:f}=n;if(s in f)i=f[s];else{const c=We(n);i=f[s]=a.call(null,t),c()}}else i=a;n.ce&&n.ce._setProp(s,i)}o[0]&&(r&&!l?i=!1:o[1]&&(i===""||i===Pt(s))&&(i=!0))}return i}const zo=new WeakMap;function Sn(e,t,s=!1){const i=s?zo:t.propsCache,n=i.get(e);if(n)return n;const r=e.props,o={},l=[];let a=!1;if(!H(e)){const c=h=>{a=!0;const[p,m]=Sn(h,t,!0);rt(o,p),m&&l.push(...m)};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!a)return et(e)&&i.set(e,fe),fe;if(W(r))for(let c=0;c<r.length;c++){const h=mt(r[c]);En(h)&&(o[h]=J)}else if(r)for(const c in r){const h=mt(c);if(En(h)){const p=r[c],m=o[h]=W(p)||H(p)?{type:p}:rt({},p),M=m.type;let D=!1,N=!0;if(W(M))for(let T=0;T<M.length;++T){const O=M[T],L=H(O)&&O.name;if(L==="Boolean"){D=!0;break}else L==="String"&&(N=!1)}else D=H(M)&&M.name==="Boolean";m[0]=D,m[1]=N,(D||K(m,"default"))&&l.push(h)}}const f=[o,l];return et(e)&&i.set(e,f),f}function En(e){return e[0]!=="$"&&!we(e)}const ni=e=>e[0]==="_"||e==="$stable",ri=e=>W(e)?e.map(jt):[jt(e)],Vo=(e,t,s)=>{if(t._n)return t;const i=G((...n)=>ri(t(...n)),s);return i._c=!1,i},Tn=(e,t,s)=>{const i=e._ctx;for(const n in e){if(ni(n))continue;const r=e[n];if(H(r))t[n]=Vo(n,r,i);else if(r!=null){const o=ri(r);t[n]=()=>o}}},Rn=(e,t)=>{const s=ri(t);e.slots.default=()=>s},Pn=(e,t,s)=>{for(const i in t)(s||!ni(i))&&(e[i]=t[i])},Uo=(e,t,s)=>{const i=e.slots=xn();if(e.vnode.shapeFlag&32){const n=t._;n?(Pn(i,t,s),s&&Si(i,"_",n,!0)):Tn(t,i)}else t&&Rn(e,t)},Ko=(e,t,s)=>{const{vnode:i,slots:n}=e;let r=!0,o=J;if(i.shapeFlag&32){const l=t._;l?s&&l===1?r=!1:Pn(n,t,s):(r=!t.$stable,Tn(t,n)),o=t}else t&&(Rn(e,t),o={default:1});if(r)for(const l in n)!ni(l)&&o[l]==null&&delete n[l]},wt=ol;function qo(e){return Go(e)}function Go(e,t){const s=Ze();s.__VUE__=!0;const{insert:i,remove:n,patchProp:r,createElement:o,createText:l,createComment:a,setText:f,setElementText:c,parentNode:h,nextSibling:p,setScopeId:m=It,insertStaticContent:M}=e,D=(u,d,g,y=null,_=null,b=null,R=void 0,E=null,S=!!d.dynamicChildren)=>{if(u===d)return;u&&!Ne(u,d)&&(y=Ce(u),lt(u,_,b,!0),u=null),d.patchFlag===-2&&(S=!1,d.dynamicChildren=null);const{type:x,ref:$,shapeFlag:P}=d;switch(x){case vs:N(u,d,g,y);break;case se:T(u,d,g,y);break;case li:u==null&&O(d,g,y,R);break;case Ot:At(u,d,g,y,_,b,R,E,S);break;default:P&1?A(u,d,g,y,_,b,R,E,S):P&6?Xt(u,d,g,y,_,b,R,E,S):(P&64||P&128)&&x.process(u,d,g,y,_,b,R,E,S,Ue)}$!=null&&_&&ds($,u&&u.ref,b,d||u,!d)},N=(u,d,g,y)=>{if(u==null)i(d.el=l(d.children),g,y);else{const _=d.el=u.el;d.children!==u.children&&f(_,d.children)}},T=(u,d,g,y)=>{u==null?i(d.el=a(d.children||""),g,y):d.el=u.el},O=(u,d,g,y)=>{[u.el,u.anchor]=M(u.children,d,g,y,u.el,u.anchor)},L=({el:u,anchor:d},g,y)=>{let _;for(;u&&u!==d;)_=p(u),i(u,g,y),u=_;i(d,g,y)},C=({el:u,anchor:d})=>{let g;for(;u&&u!==d;)g=p(u),n(u),u=g;n(d)},A=(u,d,g,y,_,b,R,E,S)=>{d.type==="svg"?R="svg":d.type==="math"&&(R="mathml"),u==null?F(d,g,y,_,b,R,E,S):nt(u,d,_,b,R,E,S)},F=(u,d,g,y,_,b,R,E)=>{let S,x;const{props:$,shapeFlag:P,transition:I,dirs:j}=u;if(S=u.el=o(u.type,b,$&&$.is,$),P&8?c(S,u.children):P&16&&X(u.children,S,null,y,_,oi(u,b),R,E),j&&ae(u,null,y,"created"),B(S,u,u.scopeId,R,y),$){for(const Q in $)Q!=="value"&&!we(Q)&&r(S,Q,null,$[Q],b,y);"value"in $&&r(S,"value",null,$.value,b),(x=$.onVnodeBeforeMount)&&Ht(x,y,u)}j&&ae(u,null,y,"beforeMount");const V=Yo(_,I);V&&I.beforeEnter(S),i(S,d,g),((x=$&&$.onVnodeMounted)||V||j)&&wt(()=>{x&&Ht(x,y,u),V&&I.enter(S),j&&ae(u,null,y,"mounted")},_)},B=(u,d,g,y,_)=>{if(g&&m(u,g),y)for(let b=0;b<y.length;b++)m(u,y[b]);if(_){let b=_.subTree;if(d===b||Nn(b.type)&&(b.ssContent===d||b.ssFallback===d)){const R=_.vnode;B(u,R,R.scopeId,R.slotScopeIds,_.parent)}}},X=(u,d,g,y,_,b,R,E,S=0)=>{for(let x=S;x<u.length;x++){const $=u[x]=E?ie(u[x]):jt(u[x]);D(null,$,d,g,y,_,b,R,E)}},nt=(u,d,g,y,_,b,R)=>{const E=d.el=u.el;let{patchFlag:S,dynamicChildren:x,dirs:$}=d;S|=u.patchFlag&16;const P=u.props||J,I=d.props||J;let j;if(g&&ce(g,!1),(j=I.onVnodeBeforeUpdate)&&Ht(j,g,d,u),$&&ae(d,u,g,"beforeUpdate"),g&&ce(g,!0),(P.innerHTML&&I.innerHTML==null||P.textContent&&I.textContent==null)&&c(E,""),x?tt(u.dynamicChildren,x,E,g,y,oi(d,_),b):R||w(u,d,E,null,g,y,oi(d,_),b,!1),S>0){if(S&16)_t(E,P,I,g,_);else if(S&2&&P.class!==I.class&&r(E,"class",null,I.class,_),S&4&&r(E,"style",P.style,I.style,_),S&8){const V=d.dynamicProps;for(let Q=0;Q<V.length;Q++){const Y=V[Q],Tt=P[Y],xt=I[Y];(xt!==Tt||Y==="value")&&r(E,Y,Tt,xt,_,g)}}S&1&&u.children!==d.children&&c(E,d.children)}else!R&&x==null&&_t(E,P,I,g,_);((j=I.onVnodeUpdated)||$)&&wt(()=>{j&&Ht(j,g,d,u),$&&ae(d,u,g,"updated")},y)},tt=(u,d,g,y,_,b,R)=>{for(let E=0;E<d.length;E++){const S=u[E],x=d[E],$=S.el&&(S.type===Ot||!Ne(S,x)||S.shapeFlag&198)?h(S.el):g;D(S,x,$,null,y,_,b,R,!0)}},_t=(u,d,g,y,_)=>{if(d!==g){if(d!==J)for(const b in d)!we(b)&&!(b in g)&&r(u,b,d[b],null,_,y);for(const b in g){if(we(b))continue;const R=g[b],E=d[b];R!==E&&b!=="value"&&r(u,b,E,R,_,y)}"value"in g&&r(u,"value",d.value,g.value,_)}},At=(u,d,g,y,_,b,R,E,S)=>{const x=d.el=u?u.el:l(""),$=d.anchor=u?u.anchor:l("");let{patchFlag:P,dynamicChildren:I,slotScopeIds:j}=d;j&&(E=E?E.concat(j):j),u==null?(i(x,g,y),i($,g,y),X(d.children||[],g,$,_,b,R,E,S)):P>0&&P&64&&I&&u.dynamicChildren?(tt(u.dynamicChildren,I,g,_,b,R,E),(d.key!=null||_&&d===_.subTree)&&Mn(u,d,!0)):w(u,d,g,$,_,b,R,E,S)},Xt=(u,d,g,y,_,b,R,E,S)=>{d.slotScopeIds=E,u==null?d.shapeFlag&512?_.ctx.activate(d,g,y,R,S):ne(d,g,y,_,b,R,S):xe(u,d,S)},ne=(u,d,g,y,_,b,R)=>{const E=u.component=dl(u,y,_);if(ln(u)&&(E.ctx.renderer=Ue),hl(E,!1,R),E.asyncDep){if(_&&_.registerDep(E,ot,R),!u.el){const S=E.subTree=k(se);T(null,S,d,g)}}else ot(E,u,d,g,_,b,R)},xe=(u,d,g)=>{const y=d.component=u.component;if(nl(u,d,g))if(y.asyncDep&&!y.asyncResolved){v(y,d,g);return}else y.next=d,y.update();else d.el=u.el,y.vnode=d},ot=(u,d,g,y,_,b,R)=>{const E=()=>{if(u.isMounted){let{next:P,bu:I,u:j,parent:V,vnode:Q}=u;{const zt=On(u);if(zt){P&&(P.el=Q.el,v(u,P,R)),zt.asyncDep.then(()=>{u.isUnmounted||E()});return}}let Y=P,Tt;ce(u,!1),P?(P.el=Q.el,v(u,P,R)):P=Q,I&&Ds(I),(Tt=P.props&&P.props.onVnodeBeforeUpdate)&&Ht(Tt,V,P,Q),ce(u,!0);const xt=$n(u),Bt=u.subTree;u.subTree=xt,D(Bt,xt,h(Bt.el),Ce(Bt),u,_,b),P.el=xt.el,Y===null&&rl(u,xt.el),j&&wt(j,_),(Tt=P.props&&P.props.onVnodeUpdated)&&wt(()=>Ht(Tt,V,P,Q),_)}else{let P;const{el:I,props:j}=d,{bm:V,m:Q,parent:Y,root:Tt,type:xt}=u,Bt=De(d);ce(u,!1),V&&Ds(V),!Bt&&(P=j&&j.onVnodeBeforeMount)&&Ht(P,Y,d),ce(u,!0);{Tt.ce&&Tt.ce._injectChildStyle(xt);const zt=u.subTree=$n(u);D(null,zt,g,y,u,_,b),d.el=zt.el}if(Q&&wt(Q,_),!Bt&&(P=j&&j.onVnodeMounted)){const zt=d;wt(()=>Ht(P,Y,zt),_)}(d.shapeFlag&256||Y&&De(Y.vnode)&&Y.vnode.shapeFlag&256)&&u.a&&wt(u.a,_),u.isMounted=!0,d=g=y=null}};u.scope.on();const S=u.effect=new Oi(E);u.scope.off();const x=u.update=S.run.bind(S),$=u.job=S.runIfDirty.bind(S);$.i=u,$.id=u.uid,S.scheduler=()=>Ys($),ce(u,!0),x()},v=(u,d,g)=>{d.component=u;const y=u.vnode.props;u.vnode=d,u.next=null,Bo(u,d.props,y,g),Ko(u,d.children,g),$t(),tn(u),kt()},w=(u,d,g,y,_,b,R,E,S=!1)=>{const x=u&&u.children,$=u?u.shapeFlag:0,P=d.children,{patchFlag:I,shapeFlag:j}=d;if(I>0){if(I&128){pt(x,P,g,y,_,b,R,E,S);return}else if(I&256){z(x,P,g,y,_,b,R,E,S);return}}j&8?($&16&&ue(x,_,b),P!==x&&c(g,P)):$&16?j&16?pt(x,P,g,y,_,b,R,E,S):ue(x,_,b,!0):($&8&&c(g,""),j&16&&X(P,g,y,_,b,R,E,S))},z=(u,d,g,y,_,b,R,E,S)=>{u=u||fe,d=d||fe;const x=u.length,$=d.length,P=Math.min(x,$);let I;for(I=0;I<P;I++){const j=d[I]=S?ie(d[I]):jt(d[I]);D(u[I],j,g,null,_,b,R,E,S)}x>$?ue(u,_,b,!0,!1,P):X(d,g,y,_,b,R,E,S,P)},pt=(u,d,g,y,_,b,R,E,S)=>{let x=0;const $=d.length;let P=u.length-1,I=$-1;for(;x<=P&&x<=I;){const j=u[x],V=d[x]=S?ie(d[x]):jt(d[x]);if(Ne(j,V))D(j,V,g,null,_,b,R,E,S);else break;x++}for(;x<=P&&x<=I;){const j=u[P],V=d[I]=S?ie(d[I]):jt(d[I]);if(Ne(j,V))D(j,V,g,null,_,b,R,E,S);else break;P--,I--}if(x>P){if(x<=I){const j=I+1,V=j<$?d[j].el:y;for(;x<=I;)D(null,d[x]=S?ie(d[x]):jt(d[x]),g,V,_,b,R,E,S),x++}}else if(x>I)for(;x<=P;)lt(u[x],_,b,!0),x++;else{const j=x,V=x,Q=new Map;for(x=V;x<=I;x++){const Rt=d[x]=S?ie(d[x]):jt(d[x]);Rt.key!=null&&Q.set(Rt.key,x)}let Y,Tt=0;const xt=I-V+1;let Bt=!1,zt=0;const Ke=new Array(xt);for(x=0;x<xt;x++)Ke[x]=0;for(x=j;x<=P;x++){const Rt=u[x];if(Tt>=xt){lt(Rt,_,b,!0);continue}let Vt;if(Rt.key!=null)Vt=Q.get(Rt.key);else for(Y=V;Y<=I;Y++)if(Ke[Y-V]===0&&Ne(Rt,d[Y])){Vt=Y;break}Vt===void 0?lt(Rt,_,b,!0):(Ke[Vt-V]=x+1,Vt>=zt?zt=Vt:Bt=!0,D(Rt,d[Vt],g,null,_,b,R,E,S),Tt++)}const yr=Bt?Xo(Ke):fe;for(Y=yr.length-1,x=xt-1;x>=0;x--){const Rt=V+x,Vt=d[Rt],xr=Rt+1<$?d[Rt+1].el:y;Ke[x]===0?D(null,Vt,g,xr,_,b,R,E,S):Bt&&(Y<0||x!==yr[Y]?bt(Vt,g,xr,2):Y--)}}},bt=(u,d,g,y,_=null)=>{const{el:b,type:R,transition:E,children:S,shapeFlag:x}=u;if(x&6){bt(u.component.subTree,d,g,y);return}if(x&128){u.suspense.move(d,g,y);return}if(x&64){R.move(u,d,g,Ue);return}if(R===Ot){i(b,d,g);for(let P=0;P<S.length;P++)bt(S[P],d,g,y);i(u.anchor,d,g);return}if(R===li){L(u,d,g);return}if(y!==2&&x&1&&E)if(y===0)E.beforeEnter(b),i(b,d,g),wt(()=>E.enter(b),_);else{const{leave:P,delayLeave:I,afterLeave:j}=E,V=()=>{u.ctx.isUnmounted?n(b):i(b,d,g)},Q=()=>{P(b,()=>{V(),j&&j()})};I?I(b,V,Q):Q()}else i(b,d,g)},lt=(u,d,g,y=!1,_=!1)=>{const{type:b,props:R,ref:E,children:S,dynamicChildren:x,shapeFlag:$,patchFlag:P,dirs:I,cacheIndex:j}=u;if(P===-2&&(_=!1),E!=null&&($t(),ds(E,null,g,u,!0),kt()),j!=null&&(d.renderCache[j]=void 0),$&256){d.ctx.deactivate(u);return}const V=$&1&&I,Q=!De(u);let Y;if(Q&&(Y=R&&R.onVnodeBeforeUnmount)&&Ht(Y,d,u),$&6)yi(u.component,g,y);else{if($&128){u.suspense.unmount(g,y);return}V&&ae(u,null,d,"beforeUnmount"),$&64?u.type.remove(u,d,g,Ue,y):x&&!x.hasOnce&&(b!==Ot||P>0&&P&64)?ue(x,d,g,!1,!0):(b===Ot&&P&384||!_&&$&16)&&ue(S,d,g),y&&Es(u)}(Q&&(Y=R&&R.onVnodeUnmounted)||V)&&wt(()=>{Y&&Ht(Y,d,u),V&&ae(u,null,d,"unmounted")},g)},Es=u=>{const{type:d,el:g,anchor:y,transition:_}=u;if(d===Ot){bi(g,y);return}if(d===li){C(u);return}const b=()=>{n(g),_&&!_.persisted&&_.afterLeave&&_.afterLeave()};if(u.shapeFlag&1&&_&&!_.persisted){const{leave:R,delayLeave:E}=_,S=()=>R(g,b);E?E(u.el,b,S):S()}else b()},bi=(u,d)=>{let g;for(;u!==d;)g=p(u),n(u),u=g;n(d)},yi=(u,d,g)=>{const{bum:y,scope:_,job:b,subTree:R,um:E,m:S,a:x,parent:$,slots:{__:P}}=u;An(S),An(x),y&&Ds(y),$&&W(P)&&P.forEach(I=>{$.renderCache[I]=void 0}),_.stop(),b&&(b.flags|=8,lt(R,u,d,g)),E&&wt(E,d),wt(()=>{u.isUnmounted=!0},d),d&&d.pendingBranch&&!d.isUnmounted&&u.asyncDep&&!u.asyncResolved&&u.suspenseId===d.pendingId&&(d.deps--,d.deps===0&&d.resolve())},ue=(u,d,g,y=!1,_=!1,b=0)=>{for(let R=b;R<u.length;R++)lt(u[R],d,g,y,_)},Ce=u=>{if(u.shapeFlag&6)return Ce(u.component.subTree);if(u.shapeFlag&128)return u.suspense.next();const d=p(u.anchor||u.el),g=d&&d[go];return g?p(g):d};let Ve=!1;const Ts=(u,d,g)=>{u==null?d._vnode&&lt(d._vnode,null,null,!0):D(d._vnode||null,u,d,null,null,null,g),d._vnode=u,Ve||(Ve=!0,tn(),en(),Ve=!1)},Ue={p:D,um:lt,m:bt,r:Es,mt:ne,mc:X,pc:w,pbc:tt,n:Ce,o:e};return{render:Ts,hydrate:void 0,createApp:Fo(Ts)}}function oi({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function ce({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Yo(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function Mn(e,t,s=!1){const i=e.children,n=t.children;if(W(i)&&W(n))for(let r=0;r<i.length;r++){const o=i[r];let l=n[r];l.shapeFlag&1&&!l.dynamicChildren&&((l.patchFlag<=0||l.patchFlag===32)&&(l=n[r]=ie(n[r]),l.el=o.el),!s&&l.patchFlag!==-2&&Mn(o,l)),l.type===vs&&(l.el=o.el),l.type===se&&!l.el&&(l.el=o.el)}}function Xo(e){const t=e.slice(),s=[0];let i,n,r,o,l;const a=e.length;for(i=0;i<a;i++){const f=e[i];if(f!==0){if(n=s[s.length-1],e[n]<f){t[i]=n,s.push(i);continue}for(r=0,o=s.length-1;r<o;)l=r+o>>1,e[s[l]]<f?r=l+1:o=l;f<e[s[r]]&&(r>0&&(t[i]=s[r-1]),s[r]=i)}}for(r=s.length,o=s[r-1];r-- >0;)s[r]=o,o=t[o];return s}function On(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:On(t)}function An(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const Jo=Symbol.for("v-scx"),Zo=()=>ms(Jo);function ee(e,t,s){return Dn(e,t,s)}function Dn(e,t,s=J){const{immediate:i,deep:n,flush:r,once:o}=s,l=rt({},s),a=t&&i||!t&&r!=="post";let f;if(Fe){if(r==="sync"){const m=Zo();f=m.__watcherHandles||(m.__watcherHandles=[])}else if(!a){const m=()=>{};return m.stop=It,m.resume=It,m.pause=It,m}}const c=ht;l.call=(m,M,D)=>Nt(m,c,M,D);let h=!1;r==="post"?l.scheduler=m=>{wt(m,c&&c.suspense)}:r!=="sync"&&(h=!0,l.scheduler=(m,M)=>{M?m():Ys(m)}),l.augmentJob=m=>{t&&(m.flags|=4),h&&(m.flags|=2,c&&(m.id=c.uid,m.i=c))};const p=lo(e,t,l);return Fe&&(f?f.push(p):a&&p()),p}function Qo(e,t,s){const i=this.proxy,n=it(e)?e.includes(".")?Ln(i,e):()=>i[e]:e.bind(i,i);let r;H(t)?r=t:(r=t.handler,s=t);const o=We(this),l=Dn(n,r.bind(i),s);return o(),l}function Ln(e,t){const s=t.split(".");return()=>{let i=e;for(let n=0;n<s.length&&i;n++)i=i[s[n]];return i}}const tl=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${mt(t)}Modifiers`]||e[`${Pt(t)}Modifiers`];function el(e,t,...s){if(e.isUnmounted)return;const i=e.vnode.props||J;let n=s;const r=t.startsWith("update:"),o=r&&tl(i,t.slice(7));o&&(o.trim&&(n=s.map(c=>it(c)?c.trim():c)),o.number&&(n=s.map(Rr)));let l,a=i[l=As(t)]||i[l=As(mt(t))];!a&&r&&(a=i[l=As(Pt(t))]),a&&Nt(a,e,6,n);const f=i[l+"Once"];if(f){if(!e.emitted)e.emitted={};else if(e.emitted[l])return;e.emitted[l]=!0,Nt(f,e,6,n)}}function In(e,t,s=!1){const i=t.emitsCache,n=i.get(e);if(n!==void 0)return n;const r=e.emits;let o={},l=!1;if(!H(e)){const a=f=>{const c=In(f,t,!0);c&&(l=!0,rt(o,c))};!s&&t.mixins.length&&t.mixins.forEach(a),e.extends&&a(e.extends),e.mixins&&e.mixins.forEach(a)}return!r&&!l?(et(e)&&i.set(e,null),null):(W(r)?r.forEach(a=>o[a]=null):rt(o,r),et(e)&&i.set(e,o),o)}function gs(e,t){return!e||!qe(t)?!1:(t=t.slice(2).replace(/Once$/,""),K(e,t[0].toLowerCase()+t.slice(1))||K(e,Pt(t))||K(e,t))}function wa(){}function $n(e){const{type:t,vnode:s,proxy:i,withProxy:n,propsOptions:[r],slots:o,attrs:l,emit:a,render:f,renderCache:c,props:h,data:p,setupState:m,ctx:M,inheritAttrs:D}=e,N=us(e);let T,O;try{if(s.shapeFlag&4){const C=n||i,A=C;T=jt(f.call(A,C,c,h,m,p,M)),O=l}else{const C=t;T=jt(C.length>1?C(h,{attrs:l,slots:o,emit:a}):C(h,null)),O=t.props?l:sl(l)}}catch(C){$e.length=0,as(C,e,1),T=k(se)}let L=T;if(O&&D!==!1){const C=Object.keys(O),{shapeFlag:A}=L;C.length&&A&7&&(r&&C.some(Ps)&&(O=il(O,r)),L=be(L,O,!1,!0))}return s.dirs&&(L=be(L,null,!1,!0),L.dirs=L.dirs?L.dirs.concat(s.dirs):s.dirs),s.transition&&Xs(L,s.transition),T=L,us(N),T}const sl=e=>{let t;for(const s in e)(s==="class"||s==="style"||qe(s))&&((t||(t={}))[s]=e[s]);return t},il=(e,t)=>{const s={};for(const i in e)(!Ps(i)||!(i.slice(9)in t))&&(s[i]=e[i]);return s};function nl(e,t,s){const{props:i,children:n,component:r}=e,{props:o,children:l,patchFlag:a}=t,f=r.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&a>=0){if(a&1024)return!0;if(a&16)return i?kn(i,o,f):!!o;if(a&8){const c=t.dynamicProps;for(let h=0;h<c.length;h++){const p=c[h];if(o[p]!==i[p]&&!gs(f,p))return!0}}}else return(n||l)&&(!l||!l.$stable)?!0:i===o?!1:i?o?kn(i,o,f):!0:!!o;return!1}function kn(e,t,s){const i=Object.keys(t);if(i.length!==Object.keys(e).length)return!0;for(let n=0;n<i.length;n++){const r=i[n];if(t[r]!==e[r]&&!gs(s,r))return!0}return!1}function rl({vnode:e,parent:t},s){for(;t;){const i=t.subTree;if(i.suspense&&i.suspense.activeBranch===e&&(i.el=e.el),i===e)(e=t.vnode).el=s,t=t.parent;else break}}const Nn=e=>e.__isSuspense;function ol(e,t){t&&t.pendingBranch?W(e)?t.effects.push(...e):t.effects.push(e):mo(e)}const Ot=Symbol.for("v-fgt"),vs=Symbol.for("v-txt"),se=Symbol.for("v-cmt"),li=Symbol.for("v-stc"),$e=[];let St=null;function Et(e=!1){$e.push(St=e?null:[])}function ll(){$e.pop(),St=$e[$e.length-1]||null}let ke=1;function Wn(e,t=!1){ke+=e,e<0&&St&&t&&(St.hasOnce=!0)}function Fn(e){return e.dynamicChildren=ke>0?St||fe:null,ll(),ke>0&&St&&St.push(e),e}function Ft(e,t,s,i,n,r){return Fn(dt(e,t,s,i,n,r,!0))}function ai(e,t,s,i,n){return Fn(k(e,t,s,i,n,!0))}function jn(e){return e?e.__v_isVNode===!0:!1}function Ne(e,t){return e.type===t.type&&e.key===t.key}const Hn=({key:e})=>e??null,_s=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?it(e)||ct(e)||H(e)?{i:Ct,r:e,k:t,f:!!s}:e:null);function dt(e,t=null,s=null,i=0,n=null,r=e===Ot?0:1,o=!1,l=!1){const a={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&Hn(t),ref:t&&_s(t),scopeId:nn,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:i,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:Ct};return l?(ui(a,s),r&128&&e.normalize(a)):s&&(a.shapeFlag|=it(s)?8:16),ke>0&&!o&&St&&(a.patchFlag>0||r&6)&&a.patchFlag!==32&&St.push(a),a}const k=al;function al(e,t=null,s=null,i=0,n=null,r=!1){if((!e||e===Oo)&&(e=se),jn(e)){const l=be(e,t,!0);return s&&ui(l,s),ke>0&&!r&&St&&(l.shapeFlag&6?St[St.indexOf(e)]=l:St.push(l)),l.patchFlag=-2,l}if(bl(e)&&(e=e.__vccOpts),t){t=cl(t);let{class:l,style:a}=t;l&&!it(l)&&(t.class=Se(l)),et(a)&&(Ks(a)&&!W(a)&&(a=rt({},a)),t.style=Qe(a))}const o=it(e)?1:Nn(e)?128:vo(e)?64:et(e)?4:H(e)?2:0;return dt(e,t,s,i,n,o,r,!0)}function cl(e){return e?Ks(e)||Cn(e)?rt({},e):e:null}function be(e,t,s=!1,i=!1){const{props:n,ref:r,patchFlag:o,children:l,transition:a}=e,f=t?Bn(n||{},t):n,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:f,key:f&&Hn(f),ref:t&&t.ref?s&&r?W(r)?r.concat(_s(t)):[r,_s(t)]:_s(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:l,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==Ot?o===-1?16:o|16:o,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:a,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&be(e.ssContent),ssFallback:e.ssFallback&&be(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return a&&i&&Xs(c,a.clone(c)),c}function Lt(e=" ",t=0){return k(vs,null,e,t)}function ci(e="",t=!1){return t?(Et(),ai(se,null,e)):k(se,null,e)}function jt(e){return e==null||typeof e=="boolean"?k(se):W(e)?k(Ot,null,e.slice()):jn(e)?ie(e):k(vs,null,String(e))}function ie(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:be(e)}function ui(e,t){let s=0;const{shapeFlag:i}=e;if(t==null)t=null;else if(W(t))s=16;else if(typeof t=="object")if(i&65){const n=t.default;n&&(n._c&&(n._d=!1),ui(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!Cn(t)?t._ctx=Ct:n===3&&Ct&&(Ct.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else H(t)?(t={default:t,_ctx:Ct},s=32):(t=String(t),i&64?(s=16,t=[Lt(t)]):s=8);e.children=t,e.shapeFlag|=s}function Bn(...e){const t={};for(let s=0;s<e.length;s++){const i=e[s];for(const n in i)if(n==="class")t.class!==i.class&&(t.class=Se([t.class,i.class]));else if(n==="style")t.style=Qe([t.style,i.style]);else if(qe(n)){const r=t[n],o=i[n];o&&r!==o&&!(W(r)&&r.includes(o))&&(t[n]=r?[].concat(r,o):o)}else n!==""&&(t[n]=i[n])}return t}function Ht(e,t,s,i=null){Nt(e,t,7,[s,i])}const ul=bn();let fl=0;function dl(e,t,s){const i=e.type,n=(t?t.appContext:e.appContext)||ul,r={uid:fl++,vnode:e,type:i,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new Lr(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:Sn(i,n),emitsOptions:In(i,n),emit:null,emitted:null,propsDefaults:J,inheritAttrs:i.inheritAttrs,ctx:J,data:J,props:J,attrs:J,slots:J,refs:J,setupState:J,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=el.bind(null,r),e.ce&&e.ce(r),r}let ht=null,bs,fi;{const e=Ze(),t=(s,i)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(i),r=>{n.length>1?n.forEach(o=>o(r)):n[0](r)}};bs=t("__VUE_INSTANCE_SETTERS__",s=>ht=s),fi=t("__VUE_SSR_SETTERS__",s=>Fe=s)}const We=e=>{const t=ht;return bs(e),e.scope.on(),()=>{e.scope.off(),bs(t)}},zn=()=>{ht&&ht.scope.off(),bs(null)};function Vn(e){return e.vnode.shapeFlag&4}let Fe=!1;function hl(e,t=!1,s=!1){t&&fi(t);const{props:i,children:n}=e.vnode,r=Vn(e);Ho(e,i,r,t),Uo(e,n,s||t);const o=r?pl(e,t):void 0;return t&&fi(!1),o}function pl(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,Do);const{setup:i}=s;if(i){$t();const n=e.setupContext=i.length>1?gl(e):null,r=We(e),o=me(i,e,0,[e.props,n]),l=Ci(o);if(kt(),r(),(l||e.sp)&&!De(e)&&on(e),l){if(o.then(zn,zn),t)return o.then(a=>{Un(e,a)}).catch(a=>{as(a,e,0)});e.asyncDep=o}else Un(e,o)}else Kn(e)}function Un(e,t,s){H(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:et(t)&&(e.setupState=Xi(t)),Kn(e)}function Kn(e,t,s){const i=e.type;e.render||(e.render=i.render||It);{const n=We(e);$t();try{Lo(e)}finally{kt(),n()}}}const ml={get(e,t){return ft(e,"get",""),e[t]}};function gl(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,ml),slots:e.slots,emit:e.emit,expose:t}}function ys(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Xi(to(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in Le)return Le[s](e)},has(t,s){return s in t||s in Le}})):e.proxy}const vl=/(?:^|[-_])(\w)/g,_l=e=>e.replace(vl,t=>t.toUpperCase()).replace(/[-_]/g,"");function qn(e,t=!0){return H(e)?e.displayName||e.name:e.name||t&&e.__name}function Gn(e,t,s=!1){let i=qn(t);if(!i&&t.__file){const n=t.__file.match(/([^/\\]+)\.\w+$/);n&&(i=n[1])}if(!i&&e&&e.parent){const n=r=>{for(const o in r)if(r[o]===t)return o};i=n(e.components||e.parent.type.components)||n(e.appContext.components)}return i?_l(i):s?"App":"Anonymous"}function bl(e){return H(e)&&"__vccOpts"in e}const Yn=(e,t)=>ro(e,t,Fe),yl="3.5.16";/**
* @vue/runtime-dom v3.5.16
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let di;const Xn=typeof window<"u"&&window.trustedTypes;if(Xn)try{di=Xn.createPolicy("vue",{createHTML:e=>e})}catch{}const Jn=di?e=>di.createHTML(e):e=>e,xl="http://www.w3.org/2000/svg",Cl="http://www.w3.org/1998/Math/MathML",Yt=typeof document<"u"?document:null,Zn=Yt&&Yt.createElement("template"),wl={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,i)=>{const n=t==="svg"?Yt.createElementNS(xl,e):t==="mathml"?Yt.createElementNS(Cl,e):s?Yt.createElement(e,{is:s}):Yt.createElement(e);return e==="select"&&i&&i.multiple!=null&&n.setAttribute("multiple",i.multiple),n},createText:e=>Yt.createTextNode(e),createComment:e=>Yt.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Yt.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,i,n,r){const o=s?s.previousSibling:t.lastChild;if(n&&(n===r||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===r||!(n=n.nextSibling)););else{Zn.innerHTML=Jn(i==="svg"?`<svg>${e}</svg>`:i==="mathml"?`<math>${e}</math>`:e);const l=Zn.content;if(i==="svg"||i==="mathml"){const a=l.firstChild;for(;a.firstChild;)l.appendChild(a.firstChild);l.removeChild(a)}t.insertBefore(l,s)}return[o?o.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},Sl=Symbol("_vtc");function El(e,t,s){const i=e[Sl];i&&(t=(t?[t,...i]:[...i]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const xs=Symbol("_vod"),Qn=Symbol("_vsh"),Tl={beforeMount(e,{value:t},{transition:s}){e[xs]=e.style.display==="none"?"":e.style.display,s&&t?s.beforeEnter(e):je(e,t)},mounted(e,{value:t},{transition:s}){s&&t&&s.enter(e)},updated(e,{value:t,oldValue:s},{transition:i}){!t!=!s&&(i?t?(i.beforeEnter(e),je(e,!0),i.enter(e)):i.leave(e,()=>{je(e,!1)}):je(e,t))},beforeUnmount(e,{value:t}){je(e,t)}};function je(e,t){e.style.display=t?e[xs]:"none",e[Qn]=!t}const Rl=Symbol(""),Pl=/(^|;)\s*display\s*:/;function Ml(e,t,s){const i=e.style,n=it(s);let r=!1;if(s&&!n){if(t)if(it(t))for(const o of t.split(";")){const l=o.slice(0,o.indexOf(":")).trim();s[l]==null&&Cs(i,l,"")}else for(const o in t)s[o]==null&&Cs(i,o,"");for(const o in s)o==="display"&&(r=!0),Cs(i,o,s[o])}else if(n){if(t!==s){const o=i[Rl];o&&(s+=";"+o),i.cssText=s,r=Pl.test(s)}}else t&&e.removeAttribute("style");xs in e&&(e[xs]=r?i.display:"",e[Qn]&&(i.display="none"))}const tr=/\s*!important$/;function Cs(e,t,s){if(W(s))s.forEach(i=>Cs(e,t,i));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const i=Ol(e,t);tr.test(s)?e.setProperty(Pt(i),s.replace(tr,""),"important"):e[i]=s}}const er=["Webkit","Moz","ms"],hi={};function Ol(e,t){const s=hi[t];if(s)return s;let i=mt(t);if(i!=="filter"&&i in e)return hi[t]=i;i=Je(i);for(let n=0;n<er.length;n++){const r=er[n]+i;if(r in e)return hi[t]=r}return t}const sr="http://www.w3.org/1999/xlink";function ir(e,t,s,i,n,r=Dr(t)){i&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(sr,t.slice(6,t.length)):e.setAttributeNS(sr,t,s):s==null||r&&!Ri(s)?e.removeAttribute(t):e.setAttribute(t,r?"":Jt(s)?String(s):s)}function nr(e,t,s,i,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?Jn(s):s);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const l=r==="OPTION"?e.getAttribute("value")||"":e.value,a=s==null?e.type==="checkbox"?"on":"":String(s);(l!==a||!("_value"in e))&&(e.value=a),s==null&&e.removeAttribute(t),e._value=s;return}let o=!1;if(s===""||s==null){const l=typeof e[t];l==="boolean"?s=Ri(s):s==null&&l==="string"?(s="",o=!0):l==="number"&&(s=0,o=!0)}try{e[t]=s}catch{}o&&e.removeAttribute(n||t)}function Al(e,t,s,i){e.addEventListener(t,s,i)}function Dl(e,t,s,i){e.removeEventListener(t,s,i)}const rr=Symbol("_vei");function Ll(e,t,s,i,n=null){const r=e[rr]||(e[rr]={}),o=r[t];if(i&&o)o.value=i;else{const[l,a]=Il(t);if(i){const f=r[t]=Nl(i,n);Al(e,l,f,a)}else o&&(Dl(e,l,o,a),r[t]=void 0)}}const or=/(?:Once|Passive|Capture)$/;function Il(e){let t;if(or.test(e)){t={};let i;for(;i=e.match(or);)e=e.slice(0,e.length-i[0].length),t[i[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):Pt(e.slice(2)),t]}let pi=0;const $l=Promise.resolve(),kl=()=>pi||($l.then(()=>pi=0),pi=Date.now());function Nl(e,t){const s=i=>{if(!i._vts)i._vts=Date.now();else if(i._vts<=s.attached)return;Nt(Wl(i,s.value),t,5,[i])};return s.value=e,s.attached=kl(),s}function Wl(e,t){if(W(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(i=>n=>!n._stopped&&i&&i(n))}else return t}const lr=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,Fl=(e,t,s,i,n,r)=>{const o=n==="svg";t==="class"?El(e,i,o):t==="style"?Ml(e,s,i):qe(t)?Ps(t)||Ll(e,t,s,i,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):jl(e,t,i,o))?(nr(e,t,i),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&ir(e,t,i,o,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!it(i))?nr(e,mt(t),i,r,t):(t==="true-value"?e._trueValue=i:t==="false-value"&&(e._falseValue=i),ir(e,t,i,o))};function jl(e,t,s,i){if(i)return!!(t==="innerHTML"||t==="textContent"||t in e&&lr(t)&&H(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="autocorrect"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return lr(t)&&it(s)?!1:t in e}const ar={};/*! #__NO_SIDE_EFFECTS__ */function Hl(e,t,s){const i=fs(e,t);Ye(i)&&rt(i,t);class n extends mi{constructor(o){super(i,o,s)}}return n.def=i,n}const Bl=typeof HTMLElement<"u"?HTMLElement:class{};class mi extends Bl{constructor(t,s={},i=fr){super(),this._def=t,this._props=s,this._createApp=i,this._isVueCE=!0,this._instance=null,this._app=null,this._nonce=this._def.nonce,this._connected=!1,this._resolved=!1,this._numberProps=null,this._styleChildren=new WeakSet,this._ob=null,this.shadowRoot&&i!==fr?this._root=this.shadowRoot:t.shadowRoot!==!1?(this.attachShadow({mode:"open"}),this._root=this.shadowRoot):this._root=this}connectedCallback(){if(!this.isConnected)return;!this.shadowRoot&&!this._resolved&&this._parseSlots(),this._connected=!0;let t=this;for(;t=t&&(t.parentNode||t.host);)if(t instanceof mi){this._parent=t;break}this._instance||(this._resolved?this._mount(this._def):t&&t._pendingResolve?this._pendingResolve=t._pendingResolve.then(()=>{this._pendingResolve=void 0,this._resolveDef()}):this._resolveDef())}_setParent(t=this._parent){t&&(this._instance.parent=t._instance,this._inheritParentContext(t))}_inheritParentContext(t=this._parent){t&&this._app&&Object.setPrototypeOf(this._app._context.provides,t._instance.provides)}disconnectedCallback(){this._connected=!1,Gs(()=>{this._connected||(this._ob&&(this._ob.disconnect(),this._ob=null),this._app&&this._app.unmount(),this._instance&&(this._instance.ce=void 0),this._app=this._instance=null)})}_resolveDef(){if(this._pendingResolve)return;for(let i=0;i<this.attributes.length;i++)this._setAttr(this.attributes[i].name);this._ob=new MutationObserver(i=>{for(const n of i)this._setAttr(n.attributeName)}),this._ob.observe(this,{attributes:!0});const t=(i,n=!1)=>{this._resolved=!0,this._pendingResolve=void 0;const{props:r,styles:o}=i;let l;if(r&&!W(r))for(const a in r){const f=r[a];(f===Number||f&&f.type===Number)&&(a in this._props&&(this._props[a]=Ei(this._props[a])),(l||(l=Object.create(null)))[mt(a)]=!0)}this._numberProps=l,this._resolveProps(i),this.shadowRoot&&this._applyStyles(o),this._mount(i)},s=this._def.__asyncLoader;s?this._pendingResolve=s().then(i=>t(this._def=i,!0)):t(this._def)}_mount(t){this._app=this._createApp(t),this._inheritParentContext(),t.configureApp&&t.configureApp(this._app),this._app._ceVNode=this._createVNode(),this._app.mount(this._root);const s=this._instance&&this._instance.exposed;if(s)for(const i in s)K(this,i)||Object.defineProperty(this,i,{get:()=>Yi(s[i])})}_resolveProps(t){const{props:s}=t,i=W(s)?s:Object.keys(s||{});for(const n of Object.keys(this))n[0]!=="_"&&i.includes(n)&&this._setProp(n,this[n]);for(const n of i.map(mt))Object.defineProperty(this,n,{get(){return this._getProp(n)},set(r){this._setProp(n,r,!0,!0)}})}_setAttr(t){if(t.startsWith("data-v-"))return;const s=this.hasAttribute(t);let i=s?this.getAttribute(t):ar;const n=mt(t);s&&this._numberProps&&this._numberProps[n]&&(i=Ei(i)),this._setProp(n,i,!1,!0)}_getProp(t){return this._props[t]}_setProp(t,s,i=!0,n=!1){if(s!==this._props[t]&&(s===ar?delete this._props[t]:(this._props[t]=s,t==="key"&&this._app&&(this._app._ceVNode.key=s)),n&&this._instance&&this._update(),i)){const r=this._ob;r&&r.disconnect(),s===!0?this.setAttribute(Pt(t),""):typeof s=="string"||typeof s=="number"?this.setAttribute(Pt(t),s+""):s||this.removeAttribute(Pt(t)),r&&r.observe(this,{attributes:!0})}}_update(){const t=this._createVNode();this._app&&(t.appContext=this._app._context),Vl(t,this._root)}_createVNode(){const t={};this.shadowRoot||(t.onVnodeMounted=t.onVnodeUpdated=this._renderSlots.bind(this));const s=k(this._def,rt(t,this._props));return this._instance||(s.ce=i=>{this._instance=i,i.ce=this,i.isCE=!0;const n=(r,o)=>{this.dispatchEvent(new CustomEvent(r,Ye(o[0])?rt({detail:o},o[0]):{detail:o}))};i.emit=(r,...o)=>{n(r,o),Pt(r)!==r&&n(Pt(r),o)},this._setParent()}),s}_applyStyles(t,s){if(!t)return;if(s){if(s===this._def||this._styleChildren.has(s))return;this._styleChildren.add(s)}const i=this._nonce;for(let n=t.length-1;n>=0;n--){const r=document.createElement("style");i&&r.setAttribute("nonce",i),r.textContent=t[n],this.shadowRoot.prepend(r)}}_parseSlots(){const t=this._slots={};let s;for(;s=this.firstChild;){const i=s.nodeType===1&&s.getAttribute("slot")||"default";(t[i]||(t[i]=[])).push(s),this.removeChild(s)}}_renderSlots(){const t=(this._teleportTarget||this).querySelectorAll("slot"),s=this._instance.type.__scopeId;for(let i=0;i<t.length;i++){const n=t[i],r=n.getAttribute("name")||"default",o=this._slots[r],l=n.parentNode;if(o)for(const a of o){if(s&&a.nodeType===1){const f=s+"-s",c=document.createTreeWalker(a,1);a.setAttribute(f,"");let h;for(;h=c.nextNode();)h.setAttribute(f,"")}l.insertBefore(a,n)}else for(;n.firstChild;)l.insertBefore(n.firstChild,n);l.removeChild(n)}}_injectChildStyle(t){this._applyStyles(t.styles,t)}_removeChildStyle(t){}}const zl=rt({patchProp:Fl},wl);let cr;function ur(){return cr||(cr=qo(zl))}const Vl=(...e)=>{ur().render(...e)},fr=(...e)=>{const t=ur().createApp(...e),{mount:s}=t;return t.mount=i=>{const n=Kl(i);if(!n)return;const r=t._component;!H(r)&&!r.render&&!r.template&&(r.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const o=s(n,!1,Ul(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),o},t};function Ul(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function Kl(e){return it(e)?document.querySelector(e):e}function ut(e,t,s,i){return new(s||(s=Promise))(function(n,r){function o(f){try{a(i.next(f))}catch(c){r(c)}}function l(f){try{a(i.throw(f))}catch(c){r(c)}}function a(f){var c;f.done?n(f.value):(c=f.value,c instanceof s?c:new s(function(h){h(c)})).then(o,l)}a((i=i.apply(e,t||[])).next())})}typeof SuppressedError=="function"&&SuppressedError;let He=class{constructor(){this.listeners={}}on(t,s,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(s),i==null?void 0:i.once){const n=()=>{this.un(t,n),this.un(t,s)};return this.on(t,n),n}return()=>this.un(t,s)}un(t,s){var i;(i=this.listeners[t])===null||i===void 0||i.delete(s)}once(t,s){return this.on(t,s,{once:!0})}unAll(){this.listeners={}}emit(t,...s){this.listeners[t]&&this.listeners[t].forEach(i=>i(...s))}};const ws={decode:function(e,t){return ut(this,void 0,void 0,function*(){const s=new AudioContext({sampleRate:t});return s.decodeAudioData(e).finally(()=>s.close())})},createBuffer:function(e,t){return typeof e[0]=="number"&&(e=[e]),function(s){const i=s[0];if(i.some(n=>n>1||n<-1)){const n=i.length;let r=0;for(let o=0;o<n;o++){const l=Math.abs(i[o]);l>r&&(r=l)}for(const o of s)for(let l=0;l<n;l++)o[l]/=r}}(e),{duration:t,length:e[0].length,sampleRate:e[0].length/t,numberOfChannels:e.length,getChannelData:s=>e==null?void 0:e[s],copyFromChannel:AudioBuffer.prototype.copyFromChannel,copyToChannel:AudioBuffer.prototype.copyToChannel}}};function dr(e,t){const s=t.xmlns?document.createElementNS(t.xmlns,e):document.createElement(e);for(const[i,n]of Object.entries(t))if(i==="children")for(const[r,o]of Object.entries(t))typeof o=="string"?s.appendChild(document.createTextNode(o)):s.appendChild(dr(r,o));else i==="style"?Object.assign(s.style,n):i==="textContent"?s.textContent=n:s.setAttribute(i,n.toString());return s}function hr(e,t,s){const i=dr(e,t||{});return s==null||s.appendChild(i),i}var ql=Object.freeze({__proto__:null,createElement:hr,default:hr});const Gl={fetchBlob:function(e,t,s){return ut(this,void 0,void 0,function*(){const i=yield fetch(e,s);if(i.status>=400)throw new Error(`Failed to fetch ${e}: ${i.status} (${i.statusText})`);return function(n,r){ut(this,void 0,void 0,function*(){if(!n.body||!n.headers)return;const o=n.body.getReader(),l=Number(n.headers.get("Content-Length"))||0;let a=0;const f=h=>ut(this,void 0,void 0,function*(){a+=(h==null?void 0:h.length)||0;const p=Math.round(a/l*100);r(p)}),c=()=>ut(this,void 0,void 0,function*(){let h;try{h=yield o.read()}catch{return}h.done||(f(h.value),yield c())});c()})}(i.clone(),t),i.blob()})}};class Yl extends He{constructor(t){super(),this.isExternalMedia=!1,t.media?(this.media=t.media,this.isExternalMedia=!0):this.media=document.createElement("audio"),t.mediaControls&&(this.media.controls=!0),t.autoplay&&(this.media.autoplay=!0),t.playbackRate!=null&&this.onMediaEvent("canplay",()=>{t.playbackRate!=null&&(this.media.playbackRate=t.playbackRate)},{once:!0})}onMediaEvent(t,s,i){return this.media.addEventListener(t,s,i),()=>this.media.removeEventListener(t,s,i)}getSrc(){return this.media.currentSrc||this.media.src||""}revokeSrc(){const t=this.getSrc();t.startsWith("blob:")&&URL.revokeObjectURL(t)}canPlayType(t){return this.media.canPlayType(t)!==""}setSrc(t,s){const i=this.getSrc();if(t&&i===t)return;this.revokeSrc();const n=s instanceof Blob&&(this.canPlayType(s.type)||!t)?URL.createObjectURL(s):t;i&&(this.media.src="");try{this.media.src=n}catch{this.media.src=t}}destroy(){this.isExternalMedia||(this.media.pause(),this.media.remove(),this.revokeSrc(),this.media.src="",this.media.load())}setMediaElement(t){this.media=t}play(){return ut(this,void 0,void 0,function*(){return this.media.play()})}pause(){this.media.pause()}isPlaying(){return!this.media.paused&&!this.media.ended}setTime(t){this.media.currentTime=Math.max(0,Math.min(t,this.getDuration()))}getDuration(){return this.media.duration}getCurrentTime(){return this.media.currentTime}getVolume(){return this.media.volume}setVolume(t){this.media.volume=t}getMuted(){return this.media.muted}setMuted(t){this.media.muted=t}getPlaybackRate(){return this.media.playbackRate}isSeeking(){return this.media.seeking}setPlaybackRate(t,s){s!=null&&(this.media.preservesPitch=s),this.media.playbackRate=t}getMediaElement(){return this.media}setSinkId(t){return this.media.setSinkId(t)}}class ye extends He{constructor(t,s){super(),this.timeouts=[],this.isScrollable=!1,this.audioData=null,this.resizeObserver=null,this.lastContainerWidth=0,this.isDragging=!1,this.subscriptions=[],this.unsubscribeOnScroll=[],this.subscriptions=[],this.options=t;const i=this.parentFromOptionsContainer(t.container);this.parent=i;const[n,r]=this.initHtml();i.appendChild(n),this.container=n,this.scrollContainer=r.querySelector(".scroll"),this.wrapper=r.querySelector(".wrapper"),this.canvasWrapper=r.querySelector(".canvases"),this.progressWrapper=r.querySelector(".progress"),this.cursor=r.querySelector(".cursor"),s&&r.appendChild(s),this.initEvents()}parentFromOptionsContainer(t){let s;if(typeof t=="string"?s=document.querySelector(t):t instanceof HTMLElement&&(s=t),!s)throw new Error("Container not found");return s}initEvents(){const t=s=>{const i=this.wrapper.getBoundingClientRect(),n=s.clientX-i.left,r=s.clientY-i.top;return[n/i.width,r/i.height]};if(this.wrapper.addEventListener("click",s=>{const[i,n]=t(s);this.emit("click",i,n)}),this.wrapper.addEventListener("dblclick",s=>{const[i,n]=t(s);this.emit("dblclick",i,n)}),this.options.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.scrollContainer.addEventListener("scroll",()=>{const{scrollLeft:s,scrollWidth:i,clientWidth:n}=this.scrollContainer,r=s/i,o=(s+n)/i;this.emit("scroll",r,o,s,s+n)}),typeof ResizeObserver=="function"){const s=this.createDelay(100);this.resizeObserver=new ResizeObserver(()=>{s().then(()=>this.onContainerResize()).catch(()=>{})}),this.resizeObserver.observe(this.scrollContainer)}}onContainerResize(){const t=this.parent.clientWidth;t===this.lastContainerWidth&&this.options.height!=="auto"||(this.lastContainerWidth=t,this.reRender())}initDrag(){this.subscriptions.push(function(t,s,i,n,r=3,o=0,l=100){if(!t)return()=>{};const a=matchMedia("(pointer: coarse)").matches;let f=()=>{};const c=h=>{if(h.button!==o)return;h.preventDefault(),h.stopPropagation();let p=h.clientX,m=h.clientY,M=!1;const D=Date.now(),N=A=>{if(A.preventDefault(),A.stopPropagation(),a&&Date.now()-D<l)return;const F=A.clientX,B=A.clientY,X=F-p,nt=B-m;if(M||Math.abs(X)>r||Math.abs(nt)>r){const tt=t.getBoundingClientRect(),{left:_t,top:At}=tt;M||(i==null||i(p-_t,m-At),M=!0),s(X,nt,F-_t,B-At),p=F,m=B}},T=A=>{if(M){const F=A.clientX,B=A.clientY,X=t.getBoundingClientRect(),{left:nt,top:tt}=X;n==null||n(F-nt,B-tt)}f()},O=A=>{A.relatedTarget&&A.relatedTarget!==document.documentElement||T(A)},L=A=>{M&&(A.stopPropagation(),A.preventDefault())},C=A=>{M&&A.preventDefault()};document.addEventListener("pointermove",N),document.addEventListener("pointerup",T),document.addEventListener("pointerout",O),document.addEventListener("pointercancel",O),document.addEventListener("touchmove",C,{passive:!1}),document.addEventListener("click",L,{capture:!0}),f=()=>{document.removeEventListener("pointermove",N),document.removeEventListener("pointerup",T),document.removeEventListener("pointerout",O),document.removeEventListener("pointercancel",O),document.removeEventListener("touchmove",C),setTimeout(()=>{document.removeEventListener("click",L,{capture:!0})},10)}};return t.addEventListener("pointerdown",c),()=>{f(),t.removeEventListener("pointerdown",c)}}(this.wrapper,(t,s,i)=>{this.emit("drag",Math.max(0,Math.min(1,i/this.wrapper.getBoundingClientRect().width)))},t=>{this.isDragging=!0,this.emit("dragstart",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))},t=>{this.isDragging=!1,this.emit("dragend",Math.max(0,Math.min(1,t/this.wrapper.getBoundingClientRect().width)))}))}getHeight(t,s){var i;const n=((i=this.audioData)===null||i===void 0?void 0:i.numberOfChannels)||1;if(t==null)return 128;if(!isNaN(Number(t)))return Number(t);if(t==="auto"){const r=this.parent.clientHeight||128;return s!=null&&s.every(o=>!o.overlay)?r/n:r}return 128}initHtml(){const t=document.createElement("div"),s=t.attachShadow({mode:"open"}),i=this.options.cspNonce&&typeof this.options.cspNonce=="string"?this.options.cspNonce.replace(/"/g,""):"";return s.innerHTML=`
      <style${i?` nonce="${i}"`:""}>
        :host {
          user-select: none;
          min-width: 1px;
        }
        :host audio {
          display: block;
          width: 100%;
        }
        :host .scroll {
          overflow-x: auto;
          overflow-y: hidden;
          width: 100%;
          position: relative;
        }
        :host .noScrollbar {
          scrollbar-color: transparent;
          scrollbar-width: none;
        }
        :host .noScrollbar::-webkit-scrollbar {
          display: none;
          -webkit-appearance: none;
        }
        :host .wrapper {
          position: relative;
          overflow: visible;
          z-index: 2;
        }
        :host .canvases {
          min-height: ${this.getHeight(this.options.height,this.options.splitChannels)}px;
        }
        :host .canvases > div {
          position: relative;
        }
        :host canvas {
          display: block;
          position: absolute;
          top: 0;
          image-rendering: pixelated;
        }
        :host .progress {
          pointer-events: none;
          position: absolute;
          z-index: 2;
          top: 0;
          left: 0;
          width: 0;
          height: 100%;
          overflow: hidden;
        }
        :host .progress > div {
          position: relative;
        }
        :host .cursor {
          pointer-events: none;
          position: absolute;
          z-index: 5;
          top: 0;
          left: 0;
          height: 100%;
          border-radius: 2px;
        }
      </style>

      <div class="scroll" part="scroll">
        <div class="wrapper" part="wrapper">
          <div class="canvases" part="canvases"></div>
          <div class="progress" part="progress"></div>
          <div class="cursor" part="cursor"></div>
        </div>
      </div>
    `,[t,s]}setOptions(t){if(this.options.container!==t.container){const s=this.parentFromOptionsContainer(t.container);s.appendChild(this.container),this.parent=s}t.dragToSeek!==!0&&typeof this.options.dragToSeek!="object"||this.initDrag(),this.options=t,this.reRender()}getWrapper(){return this.wrapper}getWidth(){return this.scrollContainer.clientWidth}getScroll(){return this.scrollContainer.scrollLeft}setScroll(t){this.scrollContainer.scrollLeft=t}setScrollPercentage(t){const{scrollWidth:s}=this.scrollContainer,i=s*t;this.setScroll(i)}destroy(){var t,s;this.subscriptions.forEach(i=>i()),this.container.remove(),(t=this.resizeObserver)===null||t===void 0||t.disconnect(),(s=this.unsubscribeOnScroll)===null||s===void 0||s.forEach(i=>i()),this.unsubscribeOnScroll=[]}createDelay(t=10){let s,i;const n=()=>{s&&clearTimeout(s),i&&i()};return this.timeouts.push(n),()=>new Promise((r,o)=>{n(),i=o,s=setTimeout(()=>{s=void 0,i=void 0,r()},t)})}convertColorValues(t){if(!Array.isArray(t))return t||"";if(t.length<2)return t[0]||"";const s=document.createElement("canvas"),i=s.getContext("2d"),n=s.height*(window.devicePixelRatio||1),r=i.createLinearGradient(0,0,0,n),o=1/(t.length-1);return t.forEach((l,a)=>{const f=a*o;r.addColorStop(f,l)}),r}getPixelRatio(){return Math.max(1,window.devicePixelRatio||1)}renderBarWaveform(t,s,i,n){const r=t[0],o=t[1]||t[0],l=r.length,{width:a,height:f}=i.canvas,c=f/2,h=this.getPixelRatio(),p=s.barWidth?s.barWidth*h:1,m=s.barGap?s.barGap*h:s.barWidth?p/2:0,M=s.barRadius||0,D=a/(p+m)/l,N=M&&"roundRect"in i?"roundRect":"rect";i.beginPath();let T=0,O=0,L=0;for(let C=0;C<=l;C++){const A=Math.round(C*D);if(A>T){const X=Math.round(O*c*n),nt=X+Math.round(L*c*n)||1;let tt=c-X;s.barAlign==="top"?tt=0:s.barAlign==="bottom"&&(tt=f-nt),i[N](T*(p+m),tt,p,nt,M),T=A,O=0,L=0}const F=Math.abs(r[C]||0),B=Math.abs(o[C]||0);F>O&&(O=F),B>L&&(L=B)}i.fill(),i.closePath()}renderLineWaveform(t,s,i,n){const r=o=>{const l=t[o]||t[0],a=l.length,{height:f}=i.canvas,c=f/2,h=i.canvas.width/a;i.moveTo(0,c);let p=0,m=0;for(let M=0;M<=a;M++){const D=Math.round(M*h);if(D>p){const T=c+(Math.round(m*c*n)||1)*(o===0?-1:1);i.lineTo(p,T),p=D,m=0}const N=Math.abs(l[M]||0);N>m&&(m=N)}i.lineTo(p,c)};i.beginPath(),r(0),r(1),i.fill(),i.closePath()}renderWaveform(t,s,i){if(i.fillStyle=this.convertColorValues(s.waveColor),s.renderFunction)return void s.renderFunction(t,i);let n=s.barHeight||1;if(s.normalize){const r=Array.from(t[0]).reduce((o,l)=>Math.max(o,Math.abs(l)),0);n=r?1/r:1}s.barWidth||s.barGap||s.barAlign?this.renderBarWaveform(t,s,i,n):this.renderLineWaveform(t,s,i,n)}renderSingleCanvas(t,s,i,n,r,o,l){const a=this.getPixelRatio(),f=document.createElement("canvas");f.width=Math.round(i*a),f.height=Math.round(n*a),f.style.width=`${i}px`,f.style.height=`${n}px`,f.style.left=`${Math.round(r)}px`,o.appendChild(f);const c=f.getContext("2d");if(this.renderWaveform(t,s,c),f.width>0&&f.height>0){const h=f.cloneNode(),p=h.getContext("2d");p.drawImage(f,0,0),p.globalCompositeOperation="source-in",p.fillStyle=this.convertColorValues(s.progressColor),p.fillRect(0,0,f.width,f.height),l.appendChild(h)}}renderMultiCanvas(t,s,i,n,r,o){const l=this.getPixelRatio(),{clientWidth:a}=this.scrollContainer,f=i/l;let c=Math.min(ye.MAX_CANVAS_WIDTH,a,f),h={};if(c===0)return;if(s.barWidth||s.barGap){const N=s.barWidth||.5,T=N+(s.barGap||N/2);c%T!=0&&(c=Math.floor(c/T)*T)}const p=N=>{if(N<0||N>=m||h[N])return;h[N]=!0;const T=N*c,O=Math.min(f-T,c);if(O<=0)return;const L=t.map(C=>{const A=Math.floor(T/f*C.length),F=Math.floor((T+O)/f*C.length);return C.slice(A,F)});this.renderSingleCanvas(L,s,O,n,T,r,o)},m=Math.ceil(f/c);if(!this.isScrollable){for(let N=0;N<m;N++)p(N);return}const M=this.scrollContainer.scrollLeft/f,D=Math.floor(M*m);if(p(D-1),p(D),p(D+1),m>1){const N=this.on("scroll",()=>{const{scrollLeft:T}=this.scrollContainer,O=Math.floor(T/f*m);Object.keys(h).length>ye.MAX_NODES&&(r.innerHTML="",o.innerHTML="",h={}),p(O-1),p(O),p(O+1)});this.unsubscribeOnScroll.push(N)}}renderChannel(t,s,i,n){var{overlay:r}=s,o=function(c,h){var p={};for(var m in c)Object.prototype.hasOwnProperty.call(c,m)&&h.indexOf(m)<0&&(p[m]=c[m]);if(c!=null&&typeof Object.getOwnPropertySymbols=="function"){var M=0;for(m=Object.getOwnPropertySymbols(c);M<m.length;M++)h.indexOf(m[M])<0&&Object.prototype.propertyIsEnumerable.call(c,m[M])&&(p[m[M]]=c[m[M]])}return p}(s,["overlay"]);const l=document.createElement("div"),a=this.getHeight(o.height,o.splitChannels);l.style.height=`${a}px`,r&&n>0&&(l.style.marginTop=`-${a}px`),this.canvasWrapper.style.minHeight=`${a}px`,this.canvasWrapper.appendChild(l);const f=l.cloneNode();this.progressWrapper.appendChild(f),this.renderMultiCanvas(t,o,i,a,l,f)}render(t){return ut(this,void 0,void 0,function*(){var s;this.timeouts.forEach(a=>a()),this.timeouts=[],this.canvasWrapper.innerHTML="",this.progressWrapper.innerHTML="",this.options.width!=null&&(this.scrollContainer.style.width=typeof this.options.width=="number"?`${this.options.width}px`:this.options.width);const i=this.getPixelRatio(),n=this.scrollContainer.clientWidth,r=Math.ceil(t.duration*(this.options.minPxPerSec||0));this.isScrollable=r>n;const o=this.options.fillParent&&!this.isScrollable,l=(o?n:r)*i;if(this.wrapper.style.width=o?"100%":`${r}px`,this.scrollContainer.style.overflowX=this.isScrollable?"auto":"hidden",this.scrollContainer.classList.toggle("noScrollbar",!!this.options.hideScrollbar),this.cursor.style.backgroundColor=`${this.options.cursorColor||this.options.progressColor}`,this.cursor.style.width=`${this.options.cursorWidth}px`,this.audioData=t,this.emit("render"),this.options.splitChannels)for(let a=0;a<t.numberOfChannels;a++){const f=Object.assign(Object.assign({},this.options),(s=this.options.splitChannels)===null||s===void 0?void 0:s[a]);this.renderChannel([t.getChannelData(a)],f,l,a)}else{const a=[t.getChannelData(0)];t.numberOfChannels>1&&a.push(t.getChannelData(1)),this.renderChannel(a,this.options,l,0)}Promise.resolve().then(()=>this.emit("rendered"))})}reRender(){if(this.unsubscribeOnScroll.forEach(i=>i()),this.unsubscribeOnScroll=[],!this.audioData)return;const{scrollWidth:t}=this.scrollContainer,{right:s}=this.progressWrapper.getBoundingClientRect();if(this.render(this.audioData),this.isScrollable&&t!==this.scrollContainer.scrollWidth){const{right:i}=this.progressWrapper.getBoundingClientRect();let n=i-s;n*=2,n=n<0?Math.floor(n):Math.ceil(n),n/=2,this.scrollContainer.scrollLeft+=n}}zoom(t){this.options.minPxPerSec=t,this.reRender()}scrollIntoView(t,s=!1){const{scrollLeft:i,scrollWidth:n,clientWidth:r}=this.scrollContainer,o=t*n,l=i,a=i+r,f=r/2;if(this.isDragging)o+30>a?this.scrollContainer.scrollLeft+=30:o-30<l&&(this.scrollContainer.scrollLeft-=30);else{(o<l||o>a)&&(this.scrollContainer.scrollLeft=o-(this.options.autoCenter?f:0));const c=o-i-f;s&&this.options.autoCenter&&c>0&&(this.scrollContainer.scrollLeft+=Math.min(c,10))}{const c=this.scrollContainer.scrollLeft,h=c/n,p=(c+r)/n;this.emit("scroll",h,p,c,c+r)}}renderProgress(t,s){if(isNaN(t))return;const i=100*t;this.canvasWrapper.style.clipPath=`polygon(${i}% 0, 100% 0, 100% 100%, ${i}% 100%)`,this.progressWrapper.style.width=`${i}%`,this.cursor.style.left=`${i}%`,this.cursor.style.transform=`translateX(-${Math.round(i)===100?this.options.cursorWidth:0}px)`,this.isScrollable&&this.options.autoScroll&&this.scrollIntoView(t,s)}exportImage(t,s,i){return ut(this,void 0,void 0,function*(){const n=this.canvasWrapper.querySelectorAll("canvas");if(!n.length)throw new Error("No waveform data");if(i==="dataURL"){const r=Array.from(n).map(o=>o.toDataURL(t,s));return Promise.resolve(r)}return Promise.all(Array.from(n).map(r=>new Promise((o,l)=>{r.toBlob(a=>{a?o(a):l(new Error("Could not export image"))},t,s)})))})}}ye.MAX_CANVAS_WIDTH=8e3,ye.MAX_NODES=10;class Xl extends He{constructor(){super(...arguments),this.unsubscribe=()=>{}}start(){this.unsubscribe=this.on("tick",()=>{requestAnimationFrame(()=>{this.emit("tick")})}),this.emit("tick")}stop(){this.unsubscribe()}destroy(){this.unsubscribe()}}class gi extends He{constructor(t=new AudioContext){super(),this.bufferNode=null,this.playStartTime=0,this.playedDuration=0,this._muted=!1,this._playbackRate=1,this._duration=void 0,this.buffer=null,this.currentSrc="",this.paused=!0,this.crossOrigin=null,this.seeking=!1,this.autoplay=!1,this.addEventListener=this.on,this.removeEventListener=this.un,this.audioContext=t,this.gainNode=this.audioContext.createGain(),this.gainNode.connect(this.audioContext.destination)}load(){return ut(this,void 0,void 0,function*(){})}get src(){return this.currentSrc}set src(t){if(this.currentSrc=t,this._duration=void 0,!t)return this.buffer=null,void this.emit("emptied");fetch(t).then(s=>{if(s.status>=400)throw new Error(`Failed to fetch ${t}: ${s.status} (${s.statusText})`);return s.arrayBuffer()}).then(s=>this.currentSrc!==t?null:this.audioContext.decodeAudioData(s)).then(s=>{this.currentSrc===t&&(this.buffer=s,this.emit("loadedmetadata"),this.emit("canplay"),this.autoplay&&this.play())})}_play(){var t;if(!this.paused)return;this.paused=!1,(t=this.bufferNode)===null||t===void 0||t.disconnect(),this.bufferNode=this.audioContext.createBufferSource(),this.buffer&&(this.bufferNode.buffer=this.buffer),this.bufferNode.playbackRate.value=this._playbackRate,this.bufferNode.connect(this.gainNode);let s=this.playedDuration*this._playbackRate;(s>=this.duration||s<0)&&(s=0,this.playedDuration=0),this.bufferNode.start(this.audioContext.currentTime,s),this.playStartTime=this.audioContext.currentTime,this.bufferNode.onended=()=>{this.currentTime>=this.duration&&(this.pause(),this.emit("ended"))}}_pause(){var t;this.paused=!0,(t=this.bufferNode)===null||t===void 0||t.stop(),this.playedDuration+=this.audioContext.currentTime-this.playStartTime}play(){return ut(this,void 0,void 0,function*(){this.paused&&(this._play(),this.emit("play"))})}pause(){this.paused||(this._pause(),this.emit("pause"))}stopAt(t){const s=t-this.currentTime,i=this.bufferNode;i==null||i.stop(this.audioContext.currentTime+s),i==null||i.addEventListener("ended",()=>{i===this.bufferNode&&(this.bufferNode=null,this.pause())},{once:!0})}setSinkId(t){return ut(this,void 0,void 0,function*(){return this.audioContext.setSinkId(t)})}get playbackRate(){return this._playbackRate}set playbackRate(t){this._playbackRate=t,this.bufferNode&&(this.bufferNode.playbackRate.value=t)}get currentTime(){return(this.paused?this.playedDuration:this.playedDuration+(this.audioContext.currentTime-this.playStartTime))*this._playbackRate}set currentTime(t){const s=!this.paused;s&&this._pause(),this.playedDuration=t/this._playbackRate,s&&this._play(),this.emit("seeking"),this.emit("timeupdate")}get duration(){var t,s;return(t=this._duration)!==null&&t!==void 0?t:((s=this.buffer)===null||s===void 0?void 0:s.duration)||0}set duration(t){this._duration=t}get volume(){return this.gainNode.gain.value}set volume(t){this.gainNode.gain.value=t,this.emit("volumechange")}get muted(){return this._muted}set muted(t){this._muted!==t&&(this._muted=t,this._muted?this.gainNode.disconnect():this.gainNode.connect(this.audioContext.destination))}canPlayType(t){return/^(audio|video)\//.test(t)}getGainNode(){return this.gainNode}getChannelData(){const t=[];if(!this.buffer)return t;const s=this.buffer.numberOfChannels;for(let i=0;i<s;i++)t.push(this.buffer.getChannelData(i));return t}}const Jl={waveColor:"#999",progressColor:"#555",cursorWidth:1,minPxPerSec:0,fillParent:!0,interact:!0,dragToSeek:!1,autoScroll:!0,autoCenter:!0,sampleRate:8e3};class Be extends Yl{static create(t){return new Be(t)}constructor(t){const s=t.media||(t.backend==="WebAudio"?new gi:void 0);super({media:s,mediaControls:t.mediaControls,autoplay:t.autoplay,playbackRate:t.audioRate}),this.plugins=[],this.decodedData=null,this.stopAtPosition=null,this.subscriptions=[],this.mediaSubscriptions=[],this.abortController=null,this.options=Object.assign({},Jl,t),this.timer=new Xl;const i=s?void 0:this.getMediaElement();this.renderer=new ye(this.options,i),this.initPlayerEvents(),this.initRendererEvents(),this.initTimerEvents(),this.initPlugins();const n=this.options.url||this.getSrc()||"";Promise.resolve().then(()=>{this.emit("init");const{peaks:r,duration:o}=this.options;(n||r&&o)&&this.load(n,r,o).catch(()=>null)})}updateProgress(t=this.getCurrentTime()){return this.renderer.renderProgress(t/this.getDuration(),this.isPlaying()),t}initTimerEvents(){this.subscriptions.push(this.timer.on("tick",()=>{if(!this.isSeeking()){const t=this.updateProgress();this.emit("timeupdate",t),this.emit("audioprocess",t),this.stopAtPosition!=null&&this.isPlaying()&&t>=this.stopAtPosition&&this.pause()}}))}initPlayerEvents(){this.isPlaying()&&(this.emit("play"),this.timer.start()),this.mediaSubscriptions.push(this.onMediaEvent("timeupdate",()=>{const t=this.updateProgress();this.emit("timeupdate",t)}),this.onMediaEvent("play",()=>{this.emit("play"),this.timer.start()}),this.onMediaEvent("pause",()=>{this.emit("pause"),this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("emptied",()=>{this.timer.stop(),this.stopAtPosition=null}),this.onMediaEvent("ended",()=>{this.emit("timeupdate",this.getDuration()),this.emit("finish"),this.stopAtPosition=null}),this.onMediaEvent("seeking",()=>{this.emit("seeking",this.getCurrentTime())}),this.onMediaEvent("error",()=>{var t;this.emit("error",(t=this.getMediaElement().error)!==null&&t!==void 0?t:new Error("Media error")),this.stopAtPosition=null}))}initRendererEvents(){this.subscriptions.push(this.renderer.on("click",(t,s)=>{this.options.interact&&(this.seekTo(t),this.emit("interaction",t*this.getDuration()),this.emit("click",t,s))}),this.renderer.on("dblclick",(t,s)=>{this.emit("dblclick",t,s)}),this.renderer.on("scroll",(t,s,i,n)=>{const r=this.getDuration();this.emit("scroll",t*r,s*r,i,n)}),this.renderer.on("render",()=>{this.emit("redraw")}),this.renderer.on("rendered",()=>{this.emit("redrawcomplete")}),this.renderer.on("dragstart",t=>{this.emit("dragstart",t)}),this.renderer.on("dragend",t=>{this.emit("dragend",t)}));{let t;this.subscriptions.push(this.renderer.on("drag",s=>{if(!this.options.interact)return;let i;this.renderer.renderProgress(s),clearTimeout(t),this.isPlaying()?i=0:this.options.dragToSeek===!0?i=200:typeof this.options.dragToSeek=="object"&&this.options.dragToSeek!==void 0&&(i=this.options.dragToSeek.debounceTime),t=setTimeout(()=>{this.seekTo(s)},i),this.emit("interaction",s*this.getDuration()),this.emit("drag",s)}))}}initPlugins(){var t;!((t=this.options.plugins)===null||t===void 0)&&t.length&&this.options.plugins.forEach(s=>{this.registerPlugin(s)})}unsubscribePlayerEvents(){this.mediaSubscriptions.forEach(t=>t()),this.mediaSubscriptions=[]}setOptions(t){this.options=Object.assign({},this.options,t),t.duration&&!t.peaks&&(this.decodedData=ws.createBuffer(this.exportPeaks(),t.duration)),t.peaks&&t.duration&&(this.decodedData=ws.createBuffer(t.peaks,t.duration)),this.renderer.setOptions(this.options),t.audioRate&&this.setPlaybackRate(t.audioRate),t.mediaControls!=null&&(this.getMediaElement().controls=t.mediaControls)}registerPlugin(t){return t._init(this),this.plugins.push(t),this.subscriptions.push(t.once("destroy",()=>{this.plugins=this.plugins.filter(s=>s!==t)})),t}getWrapper(){return this.renderer.getWrapper()}getWidth(){return this.renderer.getWidth()}getScroll(){return this.renderer.getScroll()}setScroll(t){return this.renderer.setScroll(t)}setScrollTime(t){const s=t/this.getDuration();this.renderer.setScrollPercentage(s)}getActivePlugins(){return this.plugins}loadAudio(t,s,i,n){return ut(this,void 0,void 0,function*(){var r;if(this.emit("load",t),!this.options.media&&this.isPlaying()&&this.pause(),this.decodedData=null,this.stopAtPosition=null,!s&&!i){const l=this.options.fetchParams||{};window.AbortController&&!l.signal&&(this.abortController=new AbortController,l.signal=(r=this.abortController)===null||r===void 0?void 0:r.signal);const a=c=>this.emit("loading",c);s=yield Gl.fetchBlob(t,a,l);const f=this.options.blobMimeType;f&&(s=new Blob([s],{type:f}))}this.setSrc(t,s);const o=yield new Promise(l=>{const a=n||this.getDuration();a?l(a):this.mediaSubscriptions.push(this.onMediaEvent("loadedmetadata",()=>l(this.getDuration()),{once:!0}))});if(!t&&!s){const l=this.getMediaElement();l instanceof gi&&(l.duration=o)}if(i)this.decodedData=ws.createBuffer(i,o||0);else if(s){const l=yield s.arrayBuffer();this.decodedData=yield ws.decode(l,this.options.sampleRate)}this.decodedData&&(this.emit("decode",this.getDuration()),this.renderer.render(this.decodedData)),this.emit("ready",this.getDuration())})}load(t,s,i){return ut(this,void 0,void 0,function*(){try{return yield this.loadAudio(t,void 0,s,i)}catch(n){throw this.emit("error",n),n}})}loadBlob(t,s,i){return ut(this,void 0,void 0,function*(){try{return yield this.loadAudio("",t,s,i)}catch(n){throw this.emit("error",n),n}})}zoom(t){if(!this.decodedData)throw new Error("No audio loaded");this.renderer.zoom(t),this.emit("zoom",t)}getDecodedData(){return this.decodedData}exportPeaks({channels:t=2,maxLength:s=8e3,precision:i=1e4}={}){if(!this.decodedData)throw new Error("The audio has not been decoded yet");const n=Math.min(t,this.decodedData.numberOfChannels),r=[];for(let o=0;o<n;o++){const l=this.decodedData.getChannelData(o),a=[],f=l.length/s;for(let c=0;c<s;c++){const h=l.slice(Math.floor(c*f),Math.ceil((c+1)*f));let p=0;for(let m=0;m<h.length;m++){const M=h[m];Math.abs(M)>Math.abs(p)&&(p=M)}a.push(Math.round(p*i)/i)}r.push(a)}return r}getDuration(){let t=super.getDuration()||0;return t!==0&&t!==1/0||!this.decodedData||(t=this.decodedData.duration),t}toggleInteraction(t){this.options.interact=t}setTime(t){this.stopAtPosition=null,super.setTime(t),this.updateProgress(t),this.emit("timeupdate",t)}seekTo(t){const s=this.getDuration()*t;this.setTime(s)}play(t,s){const i=Object.create(null,{play:{get:()=>super.play}});return ut(this,void 0,void 0,function*(){t!=null&&this.setTime(t);const n=yield i.play.call(this);return s!=null&&(this.media instanceof gi?this.media.stopAt(s):this.stopAtPosition=s),n})}playPause(){return ut(this,void 0,void 0,function*(){return this.isPlaying()?this.pause():this.play()})}stop(){this.pause(),this.setTime(0)}skip(t){this.setTime(this.getCurrentTime()+t)}empty(){this.load("",[[0]],.001)}setMediaElement(t){this.unsubscribePlayerEvents(),super.setMediaElement(t),this.initPlayerEvents()}exportImage(){return ut(this,arguments,void 0,function*(t="image/png",s=1,i="dataURL"){return this.renderer.exportImage(t,s,i)})}destroy(){var t;this.emit("destroy"),(t=this.abortController)===null||t===void 0||t.abort(),this.plugins.forEach(s=>s.destroy()),this.subscriptions.forEach(s=>s()),this.unsubscribePlayerEvents(),this.timer.destroy(),this.renderer.destroy(),super.destroy()}}Be.BasePlugin=class extends He{constructor(e){super(),this.subscriptions=[],this.options=e}onInit(){}_init(e){this.wavesurfer=e,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(e=>e())}},Be.dom=ql;class pr{constructor(){this.listeners={}}on(t,s,i){if(this.listeners[t]||(this.listeners[t]=new Set),this.listeners[t].add(s),i==null?void 0:i.once){const n=()=>{this.un(t,n),this.un(t,s)};return this.on(t,n),n}return()=>this.un(t,s)}un(t,s){var i;(i=this.listeners[t])===null||i===void 0||i.delete(s)}once(t,s){return this.on(t,s,{once:!0})}unAll(){this.listeners={}}emit(t,...s){this.listeners[t]&&this.listeners[t].forEach(i=>i(...s))}}class Zl extends pr{constructor(t){super(),this.subscriptions=[],this.options=t}onInit(){}_init(t){this.wavesurfer=t,this.onInit()}destroy(){this.emit("destroy"),this.subscriptions.forEach(t=>t())}}function Ss(e,t,s,i,n=3,r=0,o=100){if(!e)return()=>{};const l=matchMedia("(pointer: coarse)").matches;let a=()=>{};const f=c=>{if(c.button!==r)return;c.preventDefault(),c.stopPropagation();let h=c.clientX,p=c.clientY,m=!1;const M=Date.now(),D=C=>{if(C.preventDefault(),C.stopPropagation(),l&&Date.now()-M<o)return;const A=C.clientX,F=C.clientY,B=A-h,X=F-p;if(m||Math.abs(B)>n||Math.abs(X)>n){const nt=e.getBoundingClientRect(),{left:tt,top:_t}=nt;m||(s==null||s(h-tt,p-_t),m=!0),t(B,X,A-tt,F-_t),h=A,p=F}},N=C=>{if(m){const A=C.clientX,F=C.clientY,B=e.getBoundingClientRect(),{left:X,top:nt}=B;i==null||i(A-X,F-nt)}a()},T=C=>{C.relatedTarget&&C.relatedTarget!==document.documentElement||N(C)},O=C=>{m&&(C.stopPropagation(),C.preventDefault())},L=C=>{m&&C.preventDefault()};document.addEventListener("pointermove",D),document.addEventListener("pointerup",N),document.addEventListener("pointerout",T),document.addEventListener("pointercancel",T),document.addEventListener("touchmove",L,{passive:!1}),document.addEventListener("click",O,{capture:!0}),a=()=>{document.removeEventListener("pointermove",D),document.removeEventListener("pointerup",N),document.removeEventListener("pointerout",T),document.removeEventListener("pointercancel",T),document.removeEventListener("touchmove",L),setTimeout(()=>{document.removeEventListener("click",O,{capture:!0})},10)}};return e.addEventListener("pointerdown",f),()=>{a(),e.removeEventListener("pointerdown",f)}}function mr(e,t){const s=t.xmlns?document.createElementNS(t.xmlns,e):document.createElement(e);for(const[i,n]of Object.entries(t))if(i==="children")for(const[r,o]of Object.entries(t))typeof o=="string"?s.appendChild(document.createTextNode(o)):s.appendChild(mr(r,o));else i==="style"?Object.assign(s.style,n):i==="textContent"?s.textContent=n:s.setAttribute(i,n.toString());return s}function ze(e,t,s){const i=mr(e,t||{});return s==null||s.appendChild(i),i}class gr extends pr{constructor(t,s,i=0){var n,r,o,l,a,f,c,h,p,m;super(),this.totalDuration=s,this.numberOfChannels=i,this.minLength=0,this.maxLength=1/0,this.contentEditable=!1,this.subscriptions=[],this.subscriptions=[],this.id=t.id||`region-${Math.random().toString(32).slice(2)}`,this.start=this.clampPosition(t.start),this.end=this.clampPosition((n=t.end)!==null&&n!==void 0?n:t.start),this.drag=(r=t.drag)===null||r===void 0||r,this.resize=(o=t.resize)===null||o===void 0||o,this.resizeStart=(l=t.resizeStart)===null||l===void 0||l,this.resizeEnd=(a=t.resizeEnd)===null||a===void 0||a,this.color=(f=t.color)!==null&&f!==void 0?f:"rgba(0, 0, 0, 0.1)",this.minLength=(c=t.minLength)!==null&&c!==void 0?c:this.minLength,this.maxLength=(h=t.maxLength)!==null&&h!==void 0?h:this.maxLength,this.channelIdx=(p=t.channelIdx)!==null&&p!==void 0?p:-1,this.contentEditable=(m=t.contentEditable)!==null&&m!==void 0?m:this.contentEditable,this.element=this.initElement(),this.setContent(t.content),this.setPart(),this.renderPosition(),this.initMouseEvents()}clampPosition(t){return Math.max(0,Math.min(this.totalDuration,t))}setPart(){const t=this.start===this.end;this.element.setAttribute("part",`${t?"marker":"region"} ${this.id}`)}addResizeHandles(t){const s={position:"absolute",zIndex:"2",width:"6px",height:"100%",top:"0",cursor:"ew-resize",wordBreak:"keep-all"},i=ze("div",{part:"region-handle region-handle-left",style:Object.assign(Object.assign({},s),{left:"0",borderLeft:"2px solid rgba(0, 0, 0, 0.5)",borderRadius:"2px 0 0 2px"})},t),n=ze("div",{part:"region-handle region-handle-right",style:Object.assign(Object.assign({},s),{right:"0",borderRight:"2px solid rgba(0, 0, 0, 0.5)",borderRadius:"0 2px 2px 0"})},t);this.subscriptions.push(Ss(i,r=>this.onResize(r,"start"),()=>null,()=>this.onEndResizing(),1),Ss(n,r=>this.onResize(r,"end"),()=>null,()=>this.onEndResizing(),1))}removeResizeHandles(t){const s=t.querySelector('[part*="region-handle-left"]'),i=t.querySelector('[part*="region-handle-right"]');s&&t.removeChild(s),i&&t.removeChild(i)}initElement(){const t=this.start===this.end;let s=0,i=100;this.channelIdx>=0&&this.channelIdx<this.numberOfChannels&&(i=100/this.numberOfChannels,s=i*this.channelIdx);const n=ze("div",{style:{position:"absolute",top:`${s}%`,height:`${i}%`,backgroundColor:t?"none":this.color,borderLeft:t?"2px solid "+this.color:"none",borderRadius:"2px",boxSizing:"border-box",transition:"background-color 0.2s ease",cursor:this.drag?"grab":"default",pointerEvents:"all"}});return!t&&this.resize&&this.addResizeHandles(n),n}renderPosition(){const t=this.start/this.totalDuration,s=(this.totalDuration-this.end)/this.totalDuration;this.element.style.left=100*t+"%",this.element.style.right=100*s+"%"}toggleCursor(t){var s;this.drag&&(!((s=this.element)===null||s===void 0)&&s.style)&&(this.element.style.cursor=t?"grabbing":"grab")}initMouseEvents(){const{element:t}=this;t&&(t.addEventListener("click",s=>this.emit("click",s)),t.addEventListener("mouseenter",s=>this.emit("over",s)),t.addEventListener("mouseleave",s=>this.emit("leave",s)),t.addEventListener("dblclick",s=>this.emit("dblclick",s)),t.addEventListener("pointerdown",()=>this.toggleCursor(!0)),t.addEventListener("pointerup",()=>this.toggleCursor(!1)),this.subscriptions.push(Ss(t,s=>this.onMove(s),()=>this.toggleCursor(!0),()=>{this.toggleCursor(!1),this.drag&&this.emit("update-end")})),this.contentEditable&&this.content&&(this.content.addEventListener("click",s=>this.onContentClick(s)),this.content.addEventListener("blur",()=>this.onContentBlur())))}_onUpdate(t,s){if(!this.element.parentElement)return;const{width:i}=this.element.parentElement.getBoundingClientRect(),n=t/i*this.totalDuration,r=s&&s!=="start"?this.start:this.start+n,o=s&&s!=="end"?this.end:this.end+n,l=o-r;r>=0&&o<=this.totalDuration&&r<=o&&l>=this.minLength&&l<=this.maxLength&&(this.start=r,this.end=o,this.renderPosition(),this.emit("update",s))}onMove(t){this.drag&&this._onUpdate(t)}onResize(t,s){this.resize&&(this.resizeStart||s!=="start")&&(this.resizeEnd||s!=="end")&&this._onUpdate(t,s)}onEndResizing(){this.resize&&this.emit("update-end")}onContentClick(t){t.stopPropagation(),t.target.focus(),this.emit("click",t)}onContentBlur(){this.emit("update-end")}_setTotalDuration(t){this.totalDuration=t,this.renderPosition()}play(t){this.emit("play",t&&this.end!==this.start?this.end:void 0)}getContent(t=!1){var s;return t?this.content||void 0:this.element instanceof HTMLElement?((s=this.content)===null||s===void 0?void 0:s.innerHTML)||void 0:""}setContent(t){var s;if((s=this.content)===null||s===void 0||s.remove(),t){if(typeof t=="string"){const i=this.start===this.end;this.content=ze("div",{style:{padding:`0.2em ${i?.2:.4}em`,display:"inline-block"},textContent:t})}else this.content=t;this.contentEditable&&(this.content.contentEditable="true"),this.content.setAttribute("part","region-content"),this.element.appendChild(this.content),this.emit("content-changed")}else this.content=void 0}setOptions(t){var s,i;if(t.color&&(this.color=t.color,this.element.style.backgroundColor=this.color),t.drag!==void 0&&(this.drag=t.drag,this.element.style.cursor=this.drag?"grab":"default"),t.start!==void 0||t.end!==void 0){const n=this.start===this.end;this.start=this.clampPosition((s=t.start)!==null&&s!==void 0?s:this.start),this.end=this.clampPosition((i=t.end)!==null&&i!==void 0?i:n?this.start:this.end),this.renderPosition(),this.setPart()}if(t.content&&this.setContent(t.content),t.id&&(this.id=t.id,this.setPart()),t.resize!==void 0&&t.resize!==this.resize){const n=this.start===this.end;this.resize=t.resize,this.resize&&!n?this.addResizeHandles(this.element):this.removeResizeHandles(this.element)}t.resizeStart!==void 0&&(this.resizeStart=t.resizeStart),t.resizeEnd!==void 0&&(this.resizeEnd=t.resizeEnd)}remove(){this.emit("remove"),this.subscriptions.forEach(t=>t()),this.element.remove(),this.element=null}}class vi extends Zl{constructor(t){super(t),this.regions=[],this.regionsContainer=this.initRegionsContainer()}static create(t){return new vi(t)}onInit(){if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");this.wavesurfer.getWrapper().appendChild(this.regionsContainer);let t=[];this.subscriptions.push(this.wavesurfer.on("timeupdate",s=>{const i=this.regions.filter(n=>n.start<=s&&(n.end===n.start?n.start+.05:n.end)>=s);i.forEach(n=>{t.includes(n)||this.emit("region-in",n)}),t.forEach(n=>{i.includes(n)||this.emit("region-out",n)}),t=i}))}initRegionsContainer(){return ze("div",{style:{position:"absolute",top:"0",left:"0",width:"100%",height:"100%",zIndex:"5",pointerEvents:"none"}})}getRegions(){return this.regions}avoidOverlapping(t){t.content&&setTimeout(()=>{const s=t.content,i=s.getBoundingClientRect(),n=this.regions.map(r=>{if(r===t||!r.content)return 0;const o=r.content.getBoundingClientRect();return i.left<o.left+o.width&&o.left<i.left+i.width?o.height:0}).reduce((r,o)=>r+o,0);s.style.marginTop=`${n}px`},10)}adjustScroll(t){var s,i;const n=(i=(s=this.wavesurfer)===null||s===void 0?void 0:s.getWrapper())===null||i===void 0?void 0:i.parentElement;if(!n)return;const{clientWidth:r,scrollWidth:o}=n;if(o<=r)return;const l=n.getBoundingClientRect(),a=t.element.getBoundingClientRect(),f=a.left-l.left,c=a.right-l.left;f<0?n.scrollLeft+=f:c>r&&(n.scrollLeft+=c-r)}virtualAppend(t,s,i){const n=()=>{if(!this.wavesurfer)return;const r=this.wavesurfer.getWidth(),o=this.wavesurfer.getScroll(),l=s.clientWidth,a=this.wavesurfer.getDuration(),f=Math.round(t.start/a*l),c=f+(Math.round((t.end-t.start)/a*l)||1)>o&&f<o+r;c&&!i.parentElement?s.appendChild(i):!c&&i.parentElement&&i.remove()};setTimeout(()=>{if(!this.wavesurfer)return;n();const r=this.wavesurfer.on("scroll",n);this.subscriptions.push(t.once("remove",r),r)},0)}saveRegion(t){this.virtualAppend(t,this.regionsContainer,t.element),this.avoidOverlapping(t),this.regions.push(t);const s=[t.on("update",i=>{i||this.adjustScroll(t),this.emit("region-update",t,i)}),t.on("update-end",()=>{this.avoidOverlapping(t),this.emit("region-updated",t)}),t.on("play",i=>{var n;(n=this.wavesurfer)===null||n===void 0||n.play(t.start,i)}),t.on("click",i=>{this.emit("region-clicked",t,i)}),t.on("dblclick",i=>{this.emit("region-double-clicked",t,i)}),t.on("content-changed",()=>{this.emit("region-content-changed",t)}),t.once("remove",()=>{s.forEach(i=>i()),this.regions=this.regions.filter(i=>i!==t),this.emit("region-removed",t)})];this.subscriptions.push(...s),this.emit("region-created",t)}addRegion(t){var s,i;if(!this.wavesurfer)throw Error("WaveSurfer is not initialized");const n=this.wavesurfer.getDuration(),r=(i=(s=this.wavesurfer)===null||s===void 0?void 0:s.getDecodedData())===null||i===void 0?void 0:i.numberOfChannels,o=new gr(t,n,r);return this.emit("region-initialized",o),n?this.saveRegion(o):this.subscriptions.push(this.wavesurfer.once("ready",l=>{o._setTotalDuration(l),this.saveRegion(o)})),o}enableDragSelection(t,s=3){var i;const n=(i=this.wavesurfer)===null||i===void 0?void 0:i.getWrapper();if(!(n&&n instanceof HTMLElement))return()=>{};let r=null,o=0;return Ss(n,(l,a,f)=>{r&&r._onUpdate(l,f>o?"end":"start")},l=>{var a,f;if(o=l,!this.wavesurfer)return;const c=this.wavesurfer.getDuration(),h=(f=(a=this.wavesurfer)===null||a===void 0?void 0:a.getDecodedData())===null||f===void 0?void 0:f.numberOfChannels,{width:p}=this.wavesurfer.getWrapper().getBoundingClientRect(),m=l/p*c,M=(l+5)/p*c;r=new gr(Object.assign(Object.assign({},t),{start:m,end:M}),c,h),this.emit("region-initialized",r),this.regionsContainer.appendChild(r.element)},()=>{r&&(this.saveRegion(r),r=null)},s)}clearRegions(){this.regions.slice().forEach(t=>t.remove()),this.regions=[]}destroy(){this.clearRegions(),super.destroy(),this.regionsContainer.remove()}}const Ql={class:"waveform-player"},ta={class:"waveform-wrapper"},ea={key:0,class:"empty-state"},sa={key:0,class:"controls-panel"},ia={class:"time-display"},na={class:"current-time"},ra={class:"duration"},oa=fs({__name:"WaveformPlayer",props:{audioFile:{},isLoading:{type:Boolean,default:!1}},emits:["timeUpdate","ready","regionsSelected"],setup(e,{expose:t,emit:s}){const i=e,n=s,r=st(),o=st(null),l=st(!1),a=st(0),f=st(0),c=st(!1),h=st(!1),p=st(!1),m=st(!1),M=st(),D=st(!1),N=Yn(()=>i.isLoading||h.value),T=(v,w)=>Math.random()*(w-v)+v,O=()=>`rgba(${T(0,255)}, ${T(0,255)}, ${T(0,255)}, 0.5)`;let L=vi.create();const C=()=>O();function A(){const v=L.getRegions().map(w=>{var z;return{start:w.start,end:w.end,color:w.color||((z=w.options)==null?void 0:z.color)||C()}});n("regionsSelected",v)}ee(()=>i.audioFile,async v=>{if(v&&(!o.value&&r.value&&await F(),o.value))try{const w=URL.createObjectURL(v);h.value=!0,await o.value.load(w)}catch(w){console.error("Error loading audio file:",w)}}),ee(p,v=>{if(!v){const w=L.getRegions();w.length>1&&(w.slice(0,-1).forEach(z=>z.remove()),A())}}),ee(M,v=>{v&&(v.addEventListener("dragover",w=>{w.preventDefault(),m.value=!0}),v.addEventListener("dragleave",()=>{m.value=!1}),v.addEventListener("drop",w=>{var pt;w.preventDefault();const z=(pt=w.dataTransfer)==null?void 0:pt.getData("text/plain");if(z){const bt=L.getRegions().find(lt=>lt.id===z);bt&&(bt.remove(),A())}m.value=!1}))});const F=async()=>{if(r.value)try{o.value=Be.create({container:r.value,waveColor:"#1976d2",progressColor:"#0d47a1",cursorColor:"#ff5722",barWidth:2,barRadius:3,height:200,normalize:!0,backend:"WebAudio",dragToSeek:!1,interact:!1,plugins:[L]}),L.enableDragSelection({color:C()},0),L.on("region-created",v=>{v.setOptions({color:C()}),ne(v),ot(v),p.value||L.getRegions().forEach(z=>{z!==v&&z.remove()}),A()}),L.on("region-updated",A),L.on("region-removed",A),L.on("region-clicked",(v,w)=>{w.stopPropagation(),o.value&&(o.value.setTime(v.start),a.value=v.start,n("timeUpdate",v.start))}),o.value.on("ready",()=>{var v;c.value=!0,f.value=((v=o.value)==null?void 0:v.getDuration())||0,h.value=!1,n("ready")}),o.value.on("play",()=>{l.value=!0}),o.value.on("pause",()=>{l.value=!1}),o.value.on("finish",()=>{l.value=!1,a.value=0}),o.value.on("timeupdate",v=>{a.value=v,n("timeUpdate",v)}),o.value.on("seeking",v=>{a.value=v,n("timeUpdate",v)})}catch(v){console.error("Error initializing WaveSurfer:",v)}},B=()=>{!o.value||!c.value||(l.value?o.value.pause():o.value.play())},X=()=>{o.value&&(o.value.stop(),a.value=0)},nt=v=>{if(!o.value||!c.value)return;const w=v/f.value;o.value.seekTo(w)},tt=v=>{const w=Math.floor(v/60),z=Math.floor(v%60);return`${w.toString().padStart(2,"0")}:${z.toString().padStart(2,"0")}`},_t=()=>{D.value=!D.value,D.value?At():Xt()};function At(){L.getRegions().forEach(v=>ne(v))}function Xt(){L.getRegions().forEach(v=>xe(v))}function ne(v){var lt;if(v.__delAttached)return;const w=v.element;if(!w)return;v._origColor=v.color||((lt=v.options)==null?void 0:lt.color);const z=()=>{D.value&&v.setOptions({color:"rgba(255,0,0,0.4)"})},pt=()=>{D.value&&v.setOptions({color:v._origColor})},bt=()=>{D.value&&(v.remove(),A(),D.value=!1,Xt())};w.addEventListener("mouseenter",z),w.addEventListener("mouseleave",pt),w.addEventListener("click",bt),v.__delAttached={enter:z,leave:pt,click:bt}}function xe(v){const w=v.__delAttached;if(!w||!v.element)return;const z=v.element;z.removeEventListener("mouseenter",w.enter),z.removeEventListener("mouseleave",w.leave),z.removeEventListener("click",w.click),v.setOptions({color:v._origColor}),delete v.__delAttached}function ot(v){if(!(v!=null&&v.element))return;const w=v.element;w.setAttribute("draggable","true"),w.addEventListener("dragstart",z=>{var pt;(pt=z.dataTransfer)==null||pt.setData("text/plain",v.id),document.body.classList.add("dragging-region")}),w.addEventListener("dragend",()=>{document.body.classList.remove("dragging-region"),m.value=!1})}return cn(async()=>{await Gs(),await F()}),Js(()=>{o.value&&o.value.destroy()}),t({seekTo:nt,togglePlayPause:B,stop:X}),(v,w)=>{const z=q("v-icon"),pt=q("v-spacer"),bt=q("v-switch"),lt=q("v-btn"),Es=q("v-card-title"),bi=q("v-divider"),yi=q("v-progress-circular"),ue=q("v-overlay"),Ce=q("v-card-text"),Ve=q("v-card");return Et(),Ft("div",Ql,[k(Ve,{class:"d-flex flex-column"},{default:G(()=>[k(Es,{class:"text-h6 py-3 d-flex align-center"},{default:G(()=>[k(z,{class:"me-2"},{default:G(()=>w[1]||(w[1]=[Lt("mdi-waveform")])),_:1,__:[1]}),w[3]||(w[3]=Lt(" Audio Waveform ")),k(pt),k(bt,{modelValue:p.value,"onUpdate:modelValue":w[0]||(w[0]=Ts=>p.value=Ts),label:"Multiple",density:"compact",color:"success","hide-details":"",class:"mr-2"},null,8,["modelValue"]),k(lt,{icon:"",color:D.value?"error":void 0,onClick:_t,class:Se({shake:D.value})},{default:G(()=>[k(z,null,{default:G(()=>w[2]||(w[2]=[Lt("mdi-delete")])),_:1,__:[2]})]),_:1},8,["color","class"])]),_:1,__:[3]}),k(bi),k(Ce,{class:"flex-grow-1 d-flex flex-column pa-0"},{default:G(()=>[dt("div",ta,[k(ue,{"model-value":N.value,contained:"",opacity:"0.05"},{default:G(()=>[k(yi,{indeterminate:"",color:"primary",size:"48"})]),_:1},8,["model-value"]),v.audioFile?ci("",!0):(Et(),Ft("div",ea,[k(z,{size:"64",color:"grey-lighten-1"},{default:G(()=>w[4]||(w[4]=[Lt("mdi-waveform")])),_:1,__:[4]}),w[5]||(w[5]=dt("p",{class:"text-grey-lighten-1 mt-4"}," Load an audio file to see waveform ",-1))])),rn(dt("div",{ref_key:"waveformContainer",ref:r,class:"waveform-container"},null,512),[[Tl,v.audioFile]])]),v.audioFile?(Et(),Ft("div",sa,[k(lt,{onClick:B,color:l.value?"error":"primary",icon:l.value?"mdi-pause":"mdi-play",size:"large"},null,8,["color","icon"]),k(lt,{onClick:X,icon:"mdi-stop",variant:"outlined",class:"ml-2"}),k(pt),dt("div",ia,[dt("span",na,re(tt(a.value)),1),w[6]||(w[6]=dt("span",{class:"separator"},"/",-1)),dt("span",ra,re(tt(f.value)),1)])])):ci("",!0)]),_:1})]),_:1})])}}}),_i=(e,t)=>{const s=e.__vccOpts||e;for(const[i,n]of t)s[i]=n;return s},la=_i(oa,[["__scopeId","data-v-e038e858"]]);function vr(e){const t=[];return e.split(/\n\s*\n/).filter(i=>i.trim()).forEach(i=>{const n=i.trim().split(`
`);if(n.length<3||!n[0].match(/^\d+$/))return;const o=n[0],l=n[1].match(/^(\d{2}:\d{2}:\d{2},\d{3})\s*-->\s*(\d{2}:\d{2}:\d{2},\d{3})$/);if(!l){console.warn(`Skipping malformed SRT timestamp line: ${n[1]}`);return}const[,a,f]=l;try{const c=_r(a),h=_r(f),p=n.slice(2).join(`
`).trim();p&&t.push({id:`srt-${o}`,startTime:c,endTime:h,text:p,originalStartTime:a,originalEndTime:f})}catch(c){console.warn(`Error parsing SRT timestamps: ${c}`)}}),t.sort((i,n)=>i.startTime-n.startTime)}function _r(e){const t=e.match(/^(\d{2}):(\d{2}):(\d{2}),(\d{3})$/);if(!t)throw new Error(`Invalid SRT timestamp format: ${e}`);const s=parseInt(t[1],10),i=parseInt(t[2],10),n=parseInt(t[3],10),r=parseInt(t[4],10);if(s<0||i<0||i>=60||n<0||n>=60||r<0||r>=1e3)throw new Error(`Invalid time values in SRT timestamp: ${e}`);return s*3600+i*60+n+r/1e3}function br(e){const t=Math.floor(e),s=Math.floor(t/3600),i=Math.floor(t%3600/60),n=t%60;return s>0?`${s.toString().padStart(2,"0")}:${i.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`:`${i.toString().padStart(2,"0")}:${n.toString().padStart(2,"0")}`}const aa={class:"subtitle-panel"},ca={class:"subtitle-content"},ua={key:0,class:"empty-state"},fa=["onClick","onMouseenter"],da={class:"entry-header"},ha={class:"timestamp"},pa={class:"region-colors"},ma={class:"duration"},ga={class:"entry-text"},va=_i(fs({__name:"SubtitlePanel",props:{subtitleEntries:{},currentTime:{},isLoading:{type:Boolean},selectedRanges:{}},emits:["seekTo"],setup(e,{emit:t}){const s=e,i=t,n=st(null),r=p=>s.currentTime>=p.startTime&&s.currentTime<p.endTime,o=(p,m)=>`${br(p)} → ${br(m)}`,l=p=>`${Math.round(p*10)/10}s`,a=p=>{n.value=p},f=()=>{n.value=null},c=p=>(s.selectedRanges??[]).some(m=>p.endTime>=m.start&&p.startTime<=m.end),h=p=>(s.selectedRanges??[]).filter(m=>p.endTime>=m.start&&p.startTime<=m.end).map(m=>m.color);return(p,m)=>{const M=q("v-icon"),D=q("v-card-title"),N=q("v-divider"),T=q("v-progress-circular"),O=q("v-overlay"),L=q("v-card-text"),C=q("v-card"),A=Ao("ripple");return Et(),Ft("div",aa,[k(C,{class:"d-flex flex-column"},{default:G(()=>[k(D,{class:"text-h6 py-3"},{default:G(()=>[k(M,{class:"me-2"},{default:G(()=>m[0]||(m[0]=[Lt("mdi-subtitles")])),_:1,__:[0]}),m[1]||(m[1]=Lt(" Subtitles "))]),_:1,__:[1]}),k(N),k(L,{class:"subtitle-wrapper flex-grow-1 pa-0"},{default:G(()=>[dt("div",ca,[k(O,{"model-value":p.isLoading&&p.subtitleEntries.length===0,contained:"",opacity:"0.1"},{default:G(()=>[k(T,{indeterminate:"",color:"primary",size:"48"})]),_:1},8,["model-value"]),p.subtitleEntries.length===0&&!p.isLoading?(Et(),Ft("div",ua,[k(M,{size:"64",color:"grey-lighten-1"},{default:G(()=>m[2]||(m[2]=[Lt("mdi-subtitles-outline")])),_:1,__:[2]}),m[3]||(m[3]=dt("p",{class:"text-grey-lighten-1 mt-4"}," Load an SRT subtitle file to see entries here ",-1))])):ci("",!0),(Et(!0),Ft(Ot,null,Zs(p.subtitleEntries,F=>rn((Et(),Ft("div",{key:F.id,class:Se(["subtitle-entry",{active:r(F)},{hover:n.value===F.id},{selected:c(F)}]),onClick:B=>i("seekTo",F.startTime),onMouseenter:B=>a(F.id),onMouseleave:f},[dt("div",da,[dt("span",ha,re(o(F.startTime,F.endTime)),1),dt("div",pa,[(Et(!0),Ft(Ot,null,Zs(h(F),(B,X)=>(Et(),Ft("span",{key:X,class:"color-box",style:Qe({backgroundColor:B})},null,4))),128))]),dt("span",ma,re(l(F.endTime-F.startTime)),1)]),dt("div",ga,re(F.text),1)],42,fa)),[[A]])),128))])]),_:1})]),_:1})])}}}),[["__scopeId","data-v-eaf2afae"]]),_a={0:{audio:"https://files.catbox.moe/2b9vpf.mp3",srt:"https://files.catbox.moe/tv0ncl.srt",title:"KLER - Na vinili"},1:{audio:"https://files.catbox.moe/ov7o7h.mp3",srt:"https://files.catbox.moe/nzyec7.srt",title:"FLIT - YIZHACHOK"}},ba=Hl(_i(fs({__name:"App.ce",setup(e){const t=st(null),s=st(null),i=st(null),n=st([]),r=st(0),o=st(!1),l=st(!1),a=st(),f=st(!1),c=st(_a),h=st([]),p=async T=>{try{f.value=!1,o.value=!0,l.value=!0;const L=await(await fetch(T.srt)).text();n.value=vr(L),o.value=!1;const A=await(await fetch(T.audio)).blob(),F=A.type.split("/")[1]||"mp3",B=new File([A],`${T.title}.${F}`,{type:A.type});s.value=B}catch(O){console.error("Error loading mock data:",O),o.value=!1,l.value=!1}};ee(t,async T=>{T&&(s.value=T)}),ee(i,async T=>{if(T){o.value=!0;try{const O=await T.text();n.value=vr(O)}catch(O){console.error("Error parsing SRT file:",O)}finally{o.value=!1}}});const m=T=>{r.value=T},M=()=>{console.log("Waveform is ready"),l.value=!1},D=T=>{var O;(O=a.value)==null||O.seekTo(T)},N=T=>{h.value=T};return(T,O)=>{const L=q("v-app-bar-title"),C=q("v-spacer"),A=q("v-btn"),F=q("v-list-item-title"),B=q("v-list-item"),X=q("v-list"),nt=q("v-menu"),tt=q("v-file-input"),_t=q("v-app-bar"),At=q("v-col"),Xt=q("v-row"),ne=q("v-container"),xe=q("v-main"),ot=q("v-app");return Et(),ai(ot,{class:"app-container"},{default:G(()=>[k(_t,{color:"primary",dark:""},{default:G(()=>[k(L,null,{default:G(()=>O[3]||(O[3]=[Lt("WaveSurfer Transcript App")])),_:1,__:[3]}),k(C),k(nt,{modelValue:f.value,"onUpdate:modelValue":O[0]||(O[0]=v=>f.value=v),"offset-y":""},{activator:G(({props:v})=>[k(A,Bn(v,{class:"mr-4","prepend-icon":"mdi-database",variant:"outlined"}),{default:G(()=>O[4]||(O[4]=[Lt(" Mock Data ")])),_:2,__:[4]},1040)]),default:G(()=>[k(X,null,{default:G(()=>[(Et(!0),Ft(Ot,null,Zs(c.value,(v,w)=>(Et(),ai(B,{key:w,onClick:z=>p(v)},{default:G(()=>[k(F,null,{default:G(()=>[Lt(re(v.title),1)]),_:2},1024)]),_:2},1032,["onClick"]))),128))]),_:1})]),_:1},8,["modelValue"]),k(tt,{class:"file-input",accept:".srt",label:"Subtitles input",modelValue:i.value,"onUpdate:modelValue":O[1]||(O[1]=v=>i.value=v)},null,8,["modelValue"]),k(tt,{class:"file-input",accept:".mp3,.wav,.m4a,.ogg",label:"Audio input",modelValue:t.value,"onUpdate:modelValue":O[2]||(O[2]=v=>t.value=v)},null,8,["modelValue"])]),_:1}),k(xe,{class:"main-content"},{default:G(()=>[k(ne,{fluid:"",class:"pa-0 h-100"},{default:G(()=>[k(Xt,{"no-gutters":""},{default:G(()=>[k(At,{cols:"6",class:"subtitle-column"},{default:G(()=>[k(va,{"subtitle-entries":n.value,"current-time":r.value,"is-loading":o.value,"selected-ranges":h.value,onSeekTo:D},null,8,["subtitle-entries","current-time","is-loading","selected-ranges"])]),_:1}),k(At,{cols:"6",class:"waveform-column"},{default:G(()=>[k(la,{"audio-file":s.value,"is-loading":l.value,onTimeUpdate:m,onReady:M,onRegionsSelected:N,ref_key:"waveformPlayer",ref:a},null,8,["audio-file","is-loading"])]),_:1})]),_:1})]),_:1})]),_:1})]),_:1})}}}),[["styles",[".file-input[data-v-cd8c1a4e]{margin-top:20px;margin-right:20px;max-width:200px}.file-input[data-v-cd8c1a4e] .v-input__prepend{display:none}"]],["__scopeId","data-v-cd8c1a4e"]]));customElements.define("my-widget",ba)})();
