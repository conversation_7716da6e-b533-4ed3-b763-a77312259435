import os
import signal
import psutil
from typing import Optional


class Reef:
    _instance: Optional['Reef'] = None
    _pid_file = "reef.pid"

    def __new__(cls) -> 'Reef':
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    def __init__(self):
        if not hasattr(self, '_initialized'):
            self._initialized = True
            self.pid: Optional[int] = self._load_pid()

    def _load_pid(self) -> Optional[int]:
        """Load PID from reef.pid file if it exists and process is running."""
        if os.path.exists(self._pid_file):
            try:
                with open(self._pid_file, 'r') as f:
                    pid = int(f.read().strip())

                if psutil.pid_exists(pid):
                    return pid
                else:
                    os.remove(self._pid_file)
                    return None
            except (ValueError, IOError):
                return None
        return None

    def save_pid(self, pid: int) -> None:
        """Save PID to reef.pid file."""
        self.pid = pid
        with open(self._pid_file, 'w') as f:
            f.write(str(pid))

    @staticmethod
    def destroy() -> bool:
        """Stop reef and kill associated processes."""
        reef = Reef()

        if reef.pid is None:
            return False

        try:
            process = psutil.Process(reef.pid)

            children = process.children(recursive=True)
            for child in children:
                try:
                    child.terminate()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            psutil.wait_procs(children, timeout=5)

            for child in children:
                try:
                    if child.is_running():
                        child.kill()
                except (psutil.NoSuchProcess, psutil.AccessDenied):
                    pass

            process.terminate()
            try:
                process.wait(timeout=5)
            except psutil.TimeoutExpired:
                process.kill()
                process.wait()

            if os.path.exists(reef._pid_file):
                os.remove(reef._pid_file)

            reef.pid = None
            return True

        except (psutil.NoSuchProcess, psutil.AccessDenied, OSError) as e:
            if os.path.exists(reef._pid_file):
                os.remove(reef._pid_file)
            reef.pid = None
            return False

    def is_running(self) -> bool:
        """Check if reef process is currently running."""
        return self.pid is not None and psutil.pid_exists(self.pid)