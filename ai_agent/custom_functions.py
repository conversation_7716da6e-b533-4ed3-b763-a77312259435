import frappe
from frappe.utils import get_datetime
import pytz
import json
from typing import Optional

@frappe.whitelist()
def get_new_entries(
    doctype: str,
    since: str,
    filters: Optional[str] = None,
    fields: Optional[str] = None,
    limit: int = 50,
    order_by: str = "creation asc"
):
    """
    Get new records from a Doctype since a UTC timestamp, converting it to Frappe's system time zone.
    Accepts JSON-encoded strings for filters and fields from JS.
    """

    # Parse inputs if passed as JSON strings
    if isinstance(fields, str):
        fields = json.loads(fields)

    if isinstance(filters, str):
        filters = json.loads(filters)

    # Convert the 'since' UTC string to system-local time
    utc_dt = get_datetime(since).replace(tzinfo=pytz.utc)
    system_tz = pytz.timezone(frappe.get_system_settings("time_zone"))
    local_dt = utc_dt.astimezone(system_tz)
    local_str = local_dt.strftime('%Y-%m-%d %H:%M:%S')

    # Build filter list
    base_filters = [["creation", ">", local_str]]
    if filters:
        base_filters.extend(filters)

    selected_fields = fields or ["name", "creation"]

    if not frappe.has_permission(doctype, "read"):
        frappe.throw("Not permitted", frappe.PermissionError)

    return frappe.get_list(
        doctype,
        fields=selected_fields,
        filters=base_filters,
        order_by=order_by,
        limit_page_length=limit
    )
