#!/usr/bin/env python3
"""
Test script for database batch operations.
This script tests the performance improvements of batch operations vs individual operations.
"""

import sys
import os
import asyncio
import time
import json
from datetime import datetime, timezone

# Add the project path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import Frappe
import frappe

# Import batch operations
try:
    from db_batch_operations import (
        get_batch_manager,
        create_message_batch,
        bulk_validate_messages,
        load_seen_messages_optimized
    )
    BATCH_AVAILABLE = True
except ImportError:
    BATCH_AVAILABLE = False
    print("Batch operations not available")

def setup_frappe():
    """Initialize Frappe for testing."""
    try:
        # Initialize Frappe (adjust site name as needed)
        site_name = "32016-51127.bacloud.info"  # or your site name
        frappe.init(site=site_name)
        frappe.connect()
        print(f"✓ Connected to Frappe site: {site_name}")
        return True
    except Exception as e:
        print(f"✗ Failed to connect to Frappe: {e}")
        return False

async def test_batch_message_creation():
    """Test batch message creation performance."""
    if not BATCH_AVAILABLE:
        print("Batch operations not available for testing")
        return
    
    print("\n=== Testing Batch Message Creation ===")
    
    # Test data
    test_messages = []
    for i in range(50):
        test_messages.append({
            'chat_id': f"test_chat_{i % 5}",
            'room_id': f"test_chat_{i % 5}:test_agent",
            'message_id': f"test_msg_{int(time.time())}_{i}",
            'sender_id': f"user_{i % 10}",
            'sender_name': f"TestUser{i % 10}",
            'text': f"Test message {i}",
            'agent_name': 'test_agent',
            'timestamp': datetime.now(timezone.utc)
        })
    
    # Test individual creation (original method)
    print("Testing individual message creation...")
    start_time = time.time()
    individual_count = 0
    
    for msg_data in test_messages[:10]:  # Test with smaller subset
        try:
            message = frappe.new_doc("Message")
            message.message_id = msg_data['message_id'] + "_individual"
            message.room_id = msg_data['room_id']
            message.sender_id = msg_data['sender_id']
            message.sender_name = msg_data['sender_name']
            message.receiver = msg_data['sender_id']
            message.agent = msg_data['agent_name']
            message.text = msg_data['text']
            message.status = 'received'
            message.insert(ignore_permissions=True)
            frappe.db.commit()  # Individual commit
            individual_count += 1
        except Exception as e:
            print(f"Error in individual creation: {e}")
    
    individual_time = time.time() - start_time
    print(f"Individual creation: {individual_count} messages in {individual_time:.3f}s")
    
    # Test batch creation
    print("Testing batch message creation...")
    start_time = time.time()
    batch_manager = get_batch_manager()
    
    for msg_data in test_messages[10:20]:  # Test with different subset
        try:
            await create_message_batch(
                chat_id=msg_data['chat_id'],
                room_id=msg_data['room_id'],
                message_id=msg_data['message_id'] + "_batch",
                sender_id=msg_data['sender_id'],
                sender_name=msg_data['sender_name'],
                text=msg_data['text'],
                agent_name=msg_data['agent_name'],
                timestamp=msg_data['timestamp'],
                immediate_commit=False
            )
        except Exception as e:
            print(f"Error in batch creation: {e}")
    
    # Force commit the batch
    results = await batch_manager.commit_all_batches()
    batch_time = time.time() - start_time
    batch_count = len(results.get('messages', []))
    
    print(f"Batch creation: {batch_count} messages in {batch_time:.3f}s")
    
    # Performance comparison
    if individual_count > 0 and batch_count > 0:
        individual_rate = individual_count / individual_time
        batch_rate = batch_count / batch_time
        improvement = (batch_rate / individual_rate - 1) * 100
        print(f"\nPerformance improvement: {improvement:.1f}%")
        print(f"Individual rate: {individual_rate:.1f} messages/second")
        print(f"Batch rate: {batch_rate:.1f} messages/second")

async def test_bulk_validation():
    """Test bulk message validation performance."""
    if not BATCH_AVAILABLE:
        print("Batch operations not available for testing")
        return
    
    print("\n=== Testing Bulk Message Validation ===")
    
    # Get some existing message IDs from database
    existing_messages = frappe.get_list(
        "Message",
        fields=["message_id"],
        limit_page_length=20,
        ignore_permissions=True
    )
    
    if not existing_messages:
        print("No existing messages found for testing")
        return
    
    message_ids = [msg["message_id"] for msg in existing_messages]
    # Add some non-existent IDs
    message_ids.extend([f"fake_id_{i}" for i in range(10)])
    
    print(f"Testing validation of {len(message_ids)} message IDs...")
    
    # Test individual validation (original method)
    start_time = time.time()
    individual_valid = {}
    for msg_id in message_ids:
        if frappe.db.exists("Message", {"message_id": msg_id}):
            individual_valid[msg_id] = True
    individual_time = time.time() - start_time
    
    # Test bulk validation
    start_time = time.time()
    bulk_valid = await bulk_validate_messages(message_ids)
    bulk_time = time.time() - start_time
    
    print(f"Individual validation: {len(individual_valid)} valid IDs in {individual_time:.3f}s")
    print(f"Bulk validation: {len(bulk_valid)} valid IDs in {bulk_time:.3f}s")
    
    # Verify results are the same
    individual_set = set(individual_valid.keys())
    bulk_set = bulk_valid
    
    if individual_set == bulk_set:
        print("✓ Results match between individual and bulk validation")
        if individual_time > 0:
            improvement = (individual_time / bulk_time - 1) * 100
            print(f"Performance improvement: {improvement:.1f}%")
    else:
        print("✗ Results don't match!")
        print(f"Individual found: {individual_set}")
        print(f"Bulk found: {bulk_set}")

def test_optimized_seen_messages():
    """Test optimized seen messages loading."""
    if not BATCH_AVAILABLE:
        print("Batch operations not available for testing")
        return
    
    print("\n=== Testing Optimized Seen Messages Loading ===")
    
    # Create a test seen messages file
    test_file = "/tmp/test_seen_messages.json"
    
    # Get some real message IDs
    existing_messages = frappe.get_list(
        "Message",
        fields=["message_id"],
        limit_page_length=10,
        ignore_permissions=True
    )
    
    test_data = {
        "messages": {},
        "last_processed": {"test_chat": "2024-01-01T00:00:00Z"}
    }
    
    # Add existing and non-existing message IDs
    for i, msg in enumerate(existing_messages):
        test_data["messages"][msg["message_id"]] = True
    
    for i in range(10):
        test_data["messages"][f"fake_message_{i}"] = True
    
    # Write test file
    with open(test_file, 'w') as f:
        json.dump(test_data, f)
    
    print(f"Created test file with {len(test_data['messages'])} message IDs")
    
    # Test optimized loading
    start_time = time.time()
    result = load_seen_messages_optimized(test_file)
    optimized_time = time.time() - start_time
    
    print(f"Optimized loading: {len(result['messages'])} valid messages in {optimized_time:.3f}s")
    print(f"Filtered out {len(test_data['messages']) - len(result['messages'])} invalid messages")
    
    # Cleanup
    os.remove(test_file)

async def main():
    """Run all tests."""
    print("Database Batch Operations Test Suite")
    print("=" * 50)
    
    if not setup_frappe():
        return
    
    if not BATCH_AVAILABLE:
        print("Batch operations module not available. Please check imports.")
        return
    
    try:
        await test_batch_message_creation()
        await test_bulk_validation()
        test_optimized_seen_messages()
        
        print("\n" + "=" * 50)
        print("All tests completed!")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
