#!/usr/bin/env python3
"""
Test script for async file I/O operations.
This script tests the performance improvements of async file operations vs synchronous operations.
"""

import asyncio
import time
import json
import os
import tempfile
import shutil
from datetime import datetime
from typing import Dict, Any, List

# Import async file operations
try:
    from async_file_operations import (
        AsyncMessageLogger,
        save_seen_messages_async,
        load_seen_messages_async,
        write_config_file_async,
        read_config_file_async,
        safe_json_write_async,
        safe_json_read_async,
        get_file_performance_stats
    )
    ASYNC_AVAILABLE = True
except ImportError:
    ASYNC_AVAILABLE = False
    print("Async file operations not available")

async def test_async_message_logger():
    """Test the AsyncMessageLogger performance."""
    if not ASYNC_AVAILABLE:
        print("Async operations not available for testing")
        return
    
    print("\n=== Testing Async Message Logger ===")
    
    # Create temporary log file
    temp_dir = tempfile.mkdtemp()
    log_file = os.path.join(temp_dir, "test_messages.json")
    
    try:
        # Test data
        test_messages = []
        for i in range(200):
            test_messages.append({
                'message_id': f"test_msg_{i}",
                'chat_id': f"chat_{i % 10}",
                'sender_id': f"user_{i % 20}",
                'text': f"Test message {i} with some content to make it realistic",
                'timestamp': datetime.utcnow().isoformat(),
                'agent_name': 'test_agent'
            })
        
        # Test synchronous logging (baseline)
        print("Testing synchronous message logging...")
        sync_start = time.time()
        
        with open(log_file + "_sync", 'w') as f:
            for message in test_messages[:50]:  # Test with smaller subset
                f.write(json.dumps(message, ensure_ascii=False) + '\n')
                f.flush()  # Force write to disk
        
        sync_time = time.time() - sync_start
        sync_count = 50
        
        # Test async logging with buffering
        print("Testing async message logging with buffering...")
        async_start = time.time()
        
        logger = AsyncMessageLogger(log_file, buffer_size=25, flush_interval=5.0)
        
        for message in test_messages[50:100]:  # Different subset
            await logger.log_message(message)
        
        # Force flush
        await logger.flush_buffer()
        await logger.close()
        
        async_time = time.time() - async_start
        async_count = 50
        
        # Performance comparison
        print(f"Synchronous logging: {sync_count} messages in {sync_time:.3f}s")
        print(f"Async buffered logging: {async_count} messages in {async_time:.3f}s")
        
        if sync_time > 0 and async_time > 0:
            sync_rate = sync_count / sync_time
            async_rate = async_count / async_time
            improvement = (async_rate / sync_rate - 1) * 100
            print(f"Performance improvement: {improvement:.1f}%")
            print(f"Sync rate: {sync_rate:.1f} messages/second")
            print(f"Async rate: {async_rate:.1f} messages/second")
        
        # Verify file contents
        with open(log_file, 'r') as f:
            logged_lines = f.readlines()
        print(f"Verified: {len(logged_lines)} messages written to async log file")
        
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)

async def test_seen_messages_operations():
    """Test async seen messages save/load operations."""
    if not ASYNC_AVAILABLE:
        print("Async operations not available for testing")
        return
    
    print("\n=== Testing Async Seen Messages Operations ===")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    seen_file = os.path.join(temp_dir, "test_seen_messages.json")
    
    try:
        # Test data
        test_seen_data = {
            "messages": {},
            "last_processed": {
                "chat_123": "2024-01-01T12:00:00Z",
                "chat_456": "2024-01-01T13:00:00Z"
            },
            "_file": seen_file
        }
        
        # Add many message IDs
        for i in range(1000):
            test_seen_data["messages"][f"msg_{i}"] = True
        
        print(f"Testing with {len(test_seen_data['messages'])} message IDs...")
        
        # Test synchronous save/load
        sync_start = time.time()
        
        with open(seen_file + "_sync", 'w') as f:
            json.dump(test_seen_data, f)
        
        with open(seen_file + "_sync", 'r') as f:
            sync_loaded = json.load(f)
        
        sync_time = time.time() - sync_start
        
        # Test async save/load
        async_start = time.time()
        
        await save_seen_messages_async(test_seen_data)
        loaded_data = await load_seen_messages_async(seen_file)
        
        async_time = time.time() - async_start
        
        # Performance comparison
        print(f"Synchronous save/load: {sync_time:.3f}s")
        print(f"Async save/load: {async_time:.3f}s")
        
        if sync_time > 0 and async_time > 0:
            improvement = (sync_time / async_time - 1) * 100
            print(f"Performance improvement: {improvement:.1f}%")
        
        # Verify data integrity
        original_messages = len(test_seen_data["messages"])
        loaded_messages = len(loaded_data["messages"])
        
        if original_messages == loaded_messages:
            print("✓ Data integrity verified - all messages preserved")
        else:
            print(f"✗ Data integrity issue - {original_messages} vs {loaded_messages}")
        
        # Test last_processed preservation
        if loaded_data["last_processed"] == test_seen_data["last_processed"]:
            print("✓ Last processed data preserved")
        else:
            print("✗ Last processed data not preserved")
    
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)

async def test_config_file_operations():
    """Test async config file operations."""
    if not ASYNC_AVAILABLE:
        print("Async operations not available for testing")
        return
    
    print("\n=== Testing Async Config File Operations ===")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    config_file = os.path.join(temp_dir, "test_config.json")
    
    try:
        # Test data
        test_config = {
            "name": "test_agent",
            "api_id": "123456",
            "api_hash": "abcdef123456",
            "phone": "+1234567890",
            "session_name": "test_session",
            "settings": {
                "auto_reply": True,
                "log_level": "INFO",
                "batch_size": 50
            }
        }
        
        # Test synchronous config write/read
        sync_start = time.time()
        
        with open(config_file + "_sync", 'w') as f:
            json.dump(test_config, f, indent=4)
        
        with open(config_file + "_sync", 'r') as f:
            sync_loaded = json.load(f)
        
        sync_time = time.time() - sync_start
        
        # Test async config write/read
        async_start = time.time()
        
        success = await write_config_file_async(test_config, config_file)
        loaded_config = await read_config_file_async(config_file)
        
        async_time = time.time() - async_start
        
        # Performance comparison
        print(f"Synchronous config operations: {sync_time:.3f}s")
        print(f"Async config operations: {async_time:.3f}s")
        
        if sync_time > 0 and async_time > 0:
            improvement = (sync_time / async_time - 1) * 100
            print(f"Performance improvement: {improvement:.1f}%")
        
        # Verify operations
        if success:
            print("✓ Async config write successful")
        else:
            print("✗ Async config write failed")
        
        if loaded_config == test_config:
            print("✓ Config data integrity verified")
        else:
            print("✗ Config data integrity issue")
    
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)

async def test_concurrent_file_operations():
    """Test concurrent async file operations."""
    if not ASYNC_AVAILABLE:
        print("Async operations not available for testing")
        return
    
    print("\n=== Testing Concurrent Async File Operations ===")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Test concurrent writes
        async def write_test_file(file_index: int):
            file_path = os.path.join(temp_dir, f"test_file_{file_index}.json")
            test_data = {
                "file_index": file_index,
                "data": [f"item_{i}" for i in range(100)],
                "timestamp": datetime.utcnow().isoformat()
            }
            return await safe_json_write_async(test_data, file_path)
        
        # Test concurrent reads
        async def read_test_file(file_index: int):
            file_path = os.path.join(temp_dir, f"test_file_{file_index}.json")
            return await safe_json_read_async(file_path)
        
        # Test concurrent writes
        print("Testing concurrent file writes...")
        write_start = time.time()
        
        write_tasks = [write_test_file(i) for i in range(10)]
        write_results = await asyncio.gather(*write_tasks)
        
        write_time = time.time() - write_start
        successful_writes = sum(1 for result in write_results if result)
        
        print(f"Concurrent writes: {successful_writes}/10 successful in {write_time:.3f}s")
        
        # Test concurrent reads
        print("Testing concurrent file reads...")
        read_start = time.time()
        
        read_tasks = [read_test_file(i) for i in range(10)]
        read_results = await asyncio.gather(*read_tasks)
        
        read_time = time.time() - read_start
        successful_reads = sum(1 for result in read_results if result is not None)
        
        print(f"Concurrent reads: {successful_reads}/10 successful in {read_time:.3f}s")
        
        # Verify data integrity
        for i, data in enumerate(read_results):
            if data and data.get("file_index") == i:
                continue
            else:
                print(f"✗ Data integrity issue in file {i}")
                break
        else:
            print("✓ All concurrent operations maintained data integrity")
    
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)

async def test_performance_monitoring():
    """Test async file operation performance monitoring."""
    if not ASYNC_AVAILABLE:
        print("Async operations not available for testing")
        return
    
    print("\n=== Testing Performance Monitoring ===")
    
    # Create temporary directory
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Perform various file operations to generate stats
        for i in range(20):
            file_path = os.path.join(temp_dir, f"perf_test_{i}.json")
            test_data = {"index": i, "data": f"test_data_{i}"}
            
            await safe_json_write_async(test_data, file_path)
            await safe_json_read_async(file_path)
        
        # Get performance statistics
        stats = get_file_performance_stats()
        
        print("Performance Statistics:")
        print(f"  Total operations: {stats['total_operations']}")
        print(f"  Average time: {stats['average_time']:.3f}s")
        print(f"  Max time: {stats['max_time']:.3f}s")
        
        if stats['operations']:
            for op_name, op_stats in stats['operations'].items():
                print(f"  {op_name}:")
                print(f"    Count: {op_stats['count']}")
                print(f"    Average time: {op_stats['average_time']:.3f}s")
                print(f"    Error rate: {op_stats['error_rate']:.1%}")
    
    finally:
        # Cleanup
        shutil.rmtree(temp_dir, ignore_errors=True)

async def main():
    """Run all async file operation tests."""
    print("Async File I/O Operations Test Suite")
    print("=" * 50)
    
    if not ASYNC_AVAILABLE:
        print("Async file operations module not available. Please check imports.")
        return
    
    try:
        await test_async_message_logger()
        await test_seen_messages_operations()
        await test_config_file_operations()
        await test_concurrent_file_operations()
        await test_performance_monitoring()
        
        print("\n" + "=" * 50)
        print("All async file I/O tests completed!")
        
        # Final performance summary
        stats = get_file_performance_stats()
        if stats['total_operations'] > 0:
            print(f"\nOverall Performance Summary:")
            print(f"  Total file operations: {stats['total_operations']}")
            print(f"  Average operation time: {stats['average_time']:.3f}s")
            print(f"  Slowest operation: {stats['max_time']:.3f}s")
        
    except Exception as e:
        print(f"Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())
