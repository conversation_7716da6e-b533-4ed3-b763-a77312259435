{"actions": [], "allow_import": 1, "allow_rename": 1, "creation": "2025-07-08 09:29:10.131967", "doctype": "DocType", "engine": "InnoDB", "field_order": ["user_id", "agent_name", "title", "init_code", "telegram_session_name", "column_break_rbvy", "telegram_api_hash", "telegram_api_id", "telegram_phone", "ai_agent", "disabled", "section_break_smez", "assigned_to"], "fields": [{"fieldname": "user_id", "fieldtype": "Data", "label": "User ID"}, {"fieldname": "agent_name", "fieldtype": "Data", "label": "Agent Name"}, {"fieldname": "title", "fieldtype": "Data", "label": "Title"}, {"fieldname": "column_break_rbvy", "fieldtype": "Column Break"}, {"fieldname": "telegram_api_id", "fieldtype": "Data", "label": "Telegram API ID"}, {"fieldname": "telegram_phone", "fieldtype": "Data", "label": "Telegram Phone"}, {"fieldname": "telegram_session_name", "fieldtype": "Data", "label": "Telegram Session Name"}, {"fieldname": "telegram_api_hash", "fieldtype": "Data", "label": "API hash"}, {"default": "0", "fieldname": "disabled", "fieldtype": "Check", "label": "Disabled"}, {"fieldname": "init_code", "fieldtype": "Data", "label": "Init Code"}, {"fieldname": "assigned_to", "fieldtype": "Link", "label": "Assigned to", "options": "User"}, {"fieldname": "ai_agent", "fieldtype": "Link", "label": "AI Agent", "options": "AI Agent"}, {"fieldname": "section_break_smez", "fieldtype": "Section Break"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-08-01 16:19:26.619069", "modified_by": "Administrator", "module": "AI Agent", "name": "Agents", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}