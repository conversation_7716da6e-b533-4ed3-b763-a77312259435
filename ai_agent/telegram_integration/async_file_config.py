"""
Configuration settings for async file I/O operations.
Adjust these settings based on your system's performance characteristics.
"""

# Message logging settings
MESSAGE_LOG_BUFFER_SIZE = 100      # Number of messages to buffer before writing
MESSAGE_LOG_FLUSH_INTERVAL = 30.0  # Seconds between automatic flushes
MESSAGE_LOG_FILE = 'messages.json' # Default message log file

# File operation settings
FILE_OPERATION_TIMEOUT = 30.0      # Timeout for file operations in seconds
MAX_CONCURRENT_FILE_OPS = 10       # Maximum concurrent file operations
FILE_BUFFER_SIZE = 8192            # Buffer size for file I/O operations

# Backup settings
ENABLE_FILE_BACKUPS = True         # Create backup files before overwriting
BACKUP_FILE_SUFFIX = '.backup'     # Suffix for backup files
MAX_BACKUP_FILES = 5               # Maximum number of backup files to keep

# Performance monitoring
ENABLE_FILE_PERFORMANCE_LOGGING = True  # Log file operation performance
LOG_SLOW_FILE_OPERATIONS = True         # Log slow file operations
SLOW_FILE_OPERATION_THRESHOLD = 1.0     # Seconds - operations slower than this are logged

# Error handling
MAX_FILE_RETRY_ATTEMPTS = 3        # Maximum retry attempts for failed file operations
FILE_RETRY_DELAY = 1.0             # Delay between retry attempts in seconds
ENABLE_ATOMIC_WRITES = True        # Use atomic writes (write to temp file then rename)

# Memory management
MAX_BUFFER_MEMORY_MB = 50          # Maximum memory usage for file buffers
CLEANUP_INTERVAL = 300             # Seconds between cleanup operations

def get_async_file_config():
    """Get the current async file configuration as a dictionary."""
    return {
        'message_logging': {
            'buffer_size': MESSAGE_LOG_BUFFER_SIZE,
            'flush_interval': MESSAGE_LOG_FLUSH_INTERVAL,
            'log_file': MESSAGE_LOG_FILE
        },
        'file_operations': {
            'timeout': FILE_OPERATION_TIMEOUT,
            'max_concurrent_ops': MAX_CONCURRENT_FILE_OPS,
            'buffer_size': FILE_BUFFER_SIZE
        },
        'backup': {
            'enable_backups': ENABLE_FILE_BACKUPS,
            'backup_suffix': BACKUP_FILE_SUFFIX,
            'max_backup_files': MAX_BACKUP_FILES
        },
        'performance': {
            'enable_performance_logging': ENABLE_FILE_PERFORMANCE_LOGGING,
            'log_slow_operations': LOG_SLOW_FILE_OPERATIONS,
            'slow_operation_threshold': SLOW_FILE_OPERATION_THRESHOLD
        },
        'error_handling': {
            'max_retry_attempts': MAX_FILE_RETRY_ATTEMPTS,
            'retry_delay': FILE_RETRY_DELAY,
            'enable_atomic_writes': ENABLE_ATOMIC_WRITES
        },
        'memory': {
            'max_buffer_memory_mb': MAX_BUFFER_MEMORY_MB,
            'cleanup_interval': CLEANUP_INTERVAL
        }
    }

def update_async_file_config(**kwargs):
    """Update async file configuration settings."""
    global MESSAGE_LOG_BUFFER_SIZE, MESSAGE_LOG_FLUSH_INTERVAL, MESSAGE_LOG_FILE
    global FILE_OPERATION_TIMEOUT, MAX_CONCURRENT_FILE_OPS, FILE_BUFFER_SIZE
    global ENABLE_FILE_BACKUPS, BACKUP_FILE_SUFFIX, MAX_BACKUP_FILES
    global ENABLE_FILE_PERFORMANCE_LOGGING, LOG_SLOW_FILE_OPERATIONS
    global SLOW_FILE_OPERATION_THRESHOLD, MAX_FILE_RETRY_ATTEMPTS
    global FILE_RETRY_DELAY, ENABLE_ATOMIC_WRITES, MAX_BUFFER_MEMORY_MB
    global CLEANUP_INTERVAL
    
    # Update message logging settings
    if 'message_log_buffer_size' in kwargs:
        MESSAGE_LOG_BUFFER_SIZE = kwargs['message_log_buffer_size']
    if 'message_log_flush_interval' in kwargs:
        MESSAGE_LOG_FLUSH_INTERVAL = kwargs['message_log_flush_interval']
    if 'message_log_file' in kwargs:
        MESSAGE_LOG_FILE = kwargs['message_log_file']
    
    # Update file operation settings
    if 'file_operation_timeout' in kwargs:
        FILE_OPERATION_TIMEOUT = kwargs['file_operation_timeout']
    if 'max_concurrent_file_ops' in kwargs:
        MAX_CONCURRENT_FILE_OPS = kwargs['max_concurrent_file_ops']
    if 'file_buffer_size' in kwargs:
        FILE_BUFFER_SIZE = kwargs['file_buffer_size']
    
    # Update backup settings
    if 'enable_file_backups' in kwargs:
        ENABLE_FILE_BACKUPS = kwargs['enable_file_backups']
    if 'backup_file_suffix' in kwargs:
        BACKUP_FILE_SUFFIX = kwargs['backup_file_suffix']
    if 'max_backup_files' in kwargs:
        MAX_BACKUP_FILES = kwargs['max_backup_files']
    
    # Update performance settings
    if 'enable_file_performance_logging' in kwargs:
        ENABLE_FILE_PERFORMANCE_LOGGING = kwargs['enable_file_performance_logging']
    if 'log_slow_file_operations' in kwargs:
        LOG_SLOW_FILE_OPERATIONS = kwargs['log_slow_file_operations']
    if 'slow_file_operation_threshold' in kwargs:
        SLOW_FILE_OPERATION_THRESHOLD = kwargs['slow_file_operation_threshold']
    
    # Update error handling settings
    if 'max_file_retry_attempts' in kwargs:
        MAX_FILE_RETRY_ATTEMPTS = kwargs['max_file_retry_attempts']
    if 'file_retry_delay' in kwargs:
        FILE_RETRY_DELAY = kwargs['file_retry_delay']
    if 'enable_atomic_writes' in kwargs:
        ENABLE_ATOMIC_WRITES = kwargs['enable_atomic_writes']
    
    # Update memory settings
    if 'max_buffer_memory_mb' in kwargs:
        MAX_BUFFER_MEMORY_MB = kwargs['max_buffer_memory_mb']
    if 'cleanup_interval' in kwargs:
        CLEANUP_INTERVAL = kwargs['cleanup_interval']

def get_optimal_buffer_size(operation_type: str, file_size_mb: float = 1.0) -> int:
    """
    Get optimal buffer size based on operation type and file size.
    
    Args:
        operation_type: Type of operation ('read', 'write', 'append')
        file_size_mb: Estimated file size in MB
    
    Returns:
        Optimal buffer size for the given conditions
    """
    base_size = FILE_BUFFER_SIZE
    
    # Adjust based on file size
    if file_size_mb > 10:
        # Large files - increase buffer size
        return min(base_size * 4, 65536)
    elif file_size_mb < 0.1:
        # Small files - decrease buffer size
        return max(base_size // 4, 1024)
    else:
        # Normal files - use base size
        return base_size

def is_async_file_feature_enabled(feature_name: str) -> bool:
    """Check if a specific async file feature is enabled."""
    feature_flags = {
        'file_backups': ENABLE_FILE_BACKUPS,
        'performance_logging': ENABLE_FILE_PERFORMANCE_LOGGING,
        'slow_operation_logging': LOG_SLOW_FILE_OPERATIONS,
        'atomic_writes': ENABLE_ATOMIC_WRITES
    }
    
    return feature_flags.get(feature_name, False)

# Environment-based configuration
def load_async_file_config_from_env():
    """Load async file configuration from environment variables."""
    import os
    
    env_mappings = {
        'ASYNC_FILE_MESSAGE_LOG_BUFFER_SIZE': 'message_log_buffer_size',
        'ASYNC_FILE_MESSAGE_LOG_FLUSH_INTERVAL': 'message_log_flush_interval',
        'ASYNC_FILE_OPERATION_TIMEOUT': 'file_operation_timeout',
        'ASYNC_FILE_MAX_CONCURRENT_OPS': 'max_concurrent_file_ops',
        'ASYNC_FILE_BUFFER_SIZE': 'file_buffer_size',
        'ASYNC_FILE_ENABLE_BACKUPS': 'enable_file_backups',
        'ASYNC_FILE_MAX_BACKUP_FILES': 'max_backup_files',
        'ASYNC_FILE_ENABLE_PERFORMANCE_LOGGING': 'enable_file_performance_logging',
        'ASYNC_FILE_LOG_SLOW_OPERATIONS': 'log_slow_file_operations',
        'ASYNC_FILE_SLOW_OPERATION_THRESHOLD': 'slow_file_operation_threshold',
        'ASYNC_FILE_MAX_RETRY_ATTEMPTS': 'max_file_retry_attempts',
        'ASYNC_FILE_RETRY_DELAY': 'file_retry_delay',
        'ASYNC_FILE_ENABLE_ATOMIC_WRITES': 'enable_atomic_writes',
        'ASYNC_FILE_MAX_BUFFER_MEMORY_MB': 'max_buffer_memory_mb',
        'ASYNC_FILE_CLEANUP_INTERVAL': 'cleanup_interval'
    }
    
    config_updates = {}
    
    for env_var, config_key in env_mappings.items():
        value = os.getenv(env_var)
        if value is not None:
            # Convert to appropriate type
            if config_key.startswith('enable_'):
                config_updates[config_key] = value.lower() in ('true', '1', 'yes', 'on')
            elif any(x in config_key for x in ['size', 'attempts', 'files', 'ops']):
                try:
                    config_updates[config_key] = int(value)
                except ValueError:
                    pass
            elif any(x in config_key for x in ['interval', 'timeout', 'threshold', 'delay']):
                try:
                    config_updates[config_key] = float(value)
                except ValueError:
                    pass
            else:
                config_updates[config_key] = value
    
    if config_updates:
        update_async_file_config(**config_updates)
        return config_updates
    
    return None

# Performance tuning presets
def apply_performance_preset(preset_name: str):
    """Apply a predefined performance preset."""
    presets = {
        'high_throughput': {
            'message_log_buffer_size': 200,
            'message_log_flush_interval': 60.0,
            'file_buffer_size': 16384,
            'max_concurrent_file_ops': 20,
            'enable_atomic_writes': False  # Faster but less safe
        },
        'low_latency': {
            'message_log_buffer_size': 25,
            'message_log_flush_interval': 5.0,
            'file_buffer_size': 4096,
            'max_concurrent_file_ops': 5,
            'enable_atomic_writes': True
        },
        'memory_efficient': {
            'message_log_buffer_size': 50,
            'message_log_flush_interval': 15.0,
            'file_buffer_size': 2048,
            'max_concurrent_file_ops': 3,
            'max_buffer_memory_mb': 20
        },
        'balanced': {
            'message_log_buffer_size': 100,
            'message_log_flush_interval': 30.0,
            'file_buffer_size': 8192,
            'max_concurrent_file_ops': 10,
            'enable_atomic_writes': True
        }
    }
    
    if preset_name in presets:
        update_async_file_config(**presets[preset_name])
        return True
    return False

def get_performance_recommendations(system_info: dict = None) -> dict:
    """Get performance recommendations based on system information."""
    recommendations = {
        'preset': 'balanced',
        'settings': {},
        'notes': []
    }
    
    if system_info:
        # Adjust based on available memory
        memory_gb = system_info.get('memory_gb', 4)
        if memory_gb >= 16:
            recommendations['preset'] = 'high_throughput'
            recommendations['notes'].append('High memory system - using high throughput preset')
        elif memory_gb <= 4:
            recommendations['preset'] = 'memory_efficient'
            recommendations['notes'].append('Low memory system - using memory efficient preset')
        
        # Adjust based on storage type
        storage_type = system_info.get('storage_type', 'unknown')
        if storage_type == 'ssd':
            recommendations['settings']['file_buffer_size'] = 16384
            recommendations['notes'].append('SSD detected - increased buffer size')
        elif storage_type == 'hdd':
            recommendations['settings']['file_buffer_size'] = 4096
            recommendations['notes'].append('HDD detected - decreased buffer size')
        
        # Adjust based on CPU cores
        cpu_cores = system_info.get('cpu_cores', 4)
        if cpu_cores >= 8:
            recommendations['settings']['max_concurrent_file_ops'] = min(cpu_cores * 2, 20)
        elif cpu_cores <= 2:
            recommendations['settings']['max_concurrent_file_ops'] = 3
    
    return recommendations

# Load environment configuration on import
load_async_file_config_from_env()
