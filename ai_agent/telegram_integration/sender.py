import frappe
import random
import asyncio
from telethon import TelegramClient

def send_from_agent(agent_bot_token: str, recipient_user_id: int, message_text: str):
    """Надсилає повідомлення від імені бота до конкретного користувача Telegram."""
    api_id = frappe.conf.get("telegram_api_id")
    api_hash = frappe.conf.get("telegram_api_hash")

    # Унікальне ім'я сесії для уникнення конфліктів
    session_name = f"agent_session_{random.randint(1000, 9999)}"

    async def run():
        async with TelegramClient(session_name, api_id, api_hash) as client:
            await client.start(bot_token=agent_bot_token)
            await client.send_message(recipient_user_id, message_text)

    # Запускаємо асинхронно
    asyncio.run(run())
