import frappe
import asyncio
import time
import json
import logging
import os
from typing import List, Dict, Any, Optional, Set
from datetime import datetime
import threading

# Import configuration
try:
    from .batch_config import (
        get_batch_config,
        get_optimal_batch_size,
        is_feature_enabled,
        DEFAULT_BATCH_SIZE,
        AUTO_COMMIT_INTERVAL,
        MAX_WORKER_THREADS
    )
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    DEFAULT_BATCH_SIZE = 50
    AUTO_COMMIT_INTERVAL = 5.0
    MAX_WORKER_THREADS = 4

logger = logging.getLogger(__name__)

class DatabaseBatchManager:
    """
    Manages batched database operations to optimize performance by reducing
    the number of individual commits and database round trips.
    """

    def __init__(self, batch_size: int = None, auto_commit_interval: float = None):
        # Use configuration if available
        if CONFIG_AVAILABLE:
            config = get_batch_config()
            self.batch_size = batch_size or config['batch_sizes']['default']
            self.auto_commit_interval = auto_commit_interval or config['timing']['auto_commit_interval']
            self.enable_performance_logging = config['performance']['enable_performance_logging']
        else:
            self.batch_size = batch_size or DEFAULT_BATCH_SIZE
            self.auto_commit_interval = auto_commit_interval or AUTO_COMMIT_INTERVAL
            self.enable_performance_logging = False

        # Batch storage
        self.message_batch = []
        self.room_batch = []
        self.agent_batch = []
        self.update_batch = []

        # Tracking
        self.last_commit_time = time.time()
        self.batch_lock = threading.Lock()
        self.pending_operations = 0

        # Auto-commit task lifecycle is explicit now
        self._auto_commit_task = None

    def start(self):
        """Explicitly start the auto-commit background task."""
        if self._auto_commit_task is None or self._auto_commit_task.done():
            # Must be called from a running event loop
            self._auto_commit_task = asyncio.create_task(self._auto_commit_loop())

    async def stop(self):
        """Stop the auto-commit background task and flush any pending batches."""
        task = self._auto_commit_task
        self._auto_commit_task = None
        if task and not task.done():
            task.cancel()
            try:
                await task
            except asyncio.CancelledError:
                pass
        # Final flush
        await self.commit_all_batches()


    async def _auto_commit_loop(self):
        """Background task that commits batches periodically."""
        while True:
            try:
                await asyncio.sleep(self.auto_commit_interval)
                if self.pending_operations > 0:
                    await self.commit_all_batches()
            except Exception as e:
                logger.error(f"Error in auto-commit loop: {e}")

    async def add_message_to_batch(self, message_data: Dict[str, Any]) -> None:
        """Add a message to the batch for later insertion."""
        with self.batch_lock:
            self.message_batch.append(message_data)
            self.pending_operations += 1

        if len(self.message_batch) >= self.batch_size:
            await self.commit_message_batch()

    async def add_room_to_batch(self, room_data: Dict[str, Any]) -> None:
        """Add a room to the batch for later insertion."""
        with self.batch_lock:
            self.room_batch.append(room_data)
            self.pending_operations += 1

        if len(self.room_batch) >= self.batch_size:
            await self.commit_room_batch()

    async def add_agent_to_batch(self, agent_data: Dict[str, Any]) -> None:
        """Add an agent to the batch for later insertion."""
        with self.batch_lock:
            self.agent_batch.append(agent_data)
            self.pending_operations += 1

        if len(self.agent_batch) >= self.batch_size:
            await self.commit_agent_batch()

    async def add_update_to_batch(self, doctype: str, name: str, updates: Dict[str, Any]) -> None:
        """Add a document update to the batch."""
        with self.batch_lock:
            self.update_batch.append({
                'doctype': doctype,
                'name': name,
                'updates': updates
            })
            self.pending_operations += 1

        if len(self.update_batch) >= self.batch_size:
            await self.commit_update_batch()

    async def commit_message_batch(self) -> List[str]:
        """Commit all pending messages in a single transaction."""
        if not self.message_batch:
            return []

        with self.batch_lock:
            batch_to_process = self.message_batch.copy()
            self.message_batch.clear()

        return self._commit_messages_sync(batch_to_process)

    async def commit_room_batch(self) -> List[str]:
        """Commit all pending rooms in a single transaction."""
        if not self.room_batch:
            return []

        with self.batch_lock:
            batch_to_process = self.room_batch.copy()
            self.room_batch.clear()

        return self._commit_rooms_sync(batch_to_process)

    async def commit_agent_batch(self) -> List[str]:
        """Commit all pending agents in a single transaction."""
        if not self.agent_batch:
            return []

        with self.batch_lock:
            batch_to_process = self.agent_batch.copy()
            self.agent_batch.clear()

        return self._commit_agents_sync(batch_to_process)

    async def commit_update_batch(self) -> int:
        """Commit all pending updates in a single transaction."""
        if not self.update_batch:
            return 0

        with self.batch_lock:
            batch_to_process = self.update_batch.copy()
            self.update_batch.clear()

        return self._commit_updates_sync(batch_to_process)

    async def commit_all_batches(self) -> Dict[str, Any]:
        """Commit all pending batches."""
        results = {}

        if self.message_batch:
            results['messages'] = await self.commit_message_batch()
        if self.room_batch:
            results['rooms'] = await self.commit_room_batch()
        if self.agent_batch:
            results['agents'] = await self.commit_agent_batch()
        if self.update_batch:
            results['updates'] = await self.commit_update_batch()

        with self.batch_lock:
            self.pending_operations = 0
            self.last_commit_time = time.time()

        return results


    def _commit_messages_sync(self, messages: List[Dict[str, Any]]) -> List[str]:
        """Synchronously commit a batch of messages using bulk_insert where possible."""
        if not messages:
            return []

        inserted_names: List[str] = []
        try:
            # 1) de-duplicate by message_id within the batch
            dedup = {}
            for m in messages:
                mid = m.get('message_id')
                if mid and mid not in dedup:
                    dedup[mid] = m
            batch = list(dedup.values())

            # 2) prefetch existing message_ids
            mids = [m.get('message_id') for m in batch if m.get('message_id')]
            existing = set()
            if mids:
                existing_rows = frappe.get_list(
                    "Message",
                    filters={"message_id": ["in", mids]},
                    fields=["name", "message_id"],
                    ignore_permissions=True,
                )
                existing = {r["message_id"] for r in existing_rows}

            to_insert = [m for m in batch if m.get('message_id') not in existing]

            if to_insert:
                fields = [
                    "message_id","room_id","sender_id","sender_name","receiver",
                    "agent","text","status","is_response_template","reply_to_message_id",
                    "media_url","media_type","timestamp"
                ]
                values = [
                    (
                        m.get('message_id'), m.get('room_id'), m.get('sender_id'),
                        m.get('sender_name', 'UnknownUser'), m.get('receiver_id', m.get('sender_id')),
                        m.get('agent_name'), m.get('text', ''), m.get('status', 'received'),
                        int(bool(m.get('is_response_template', False))), m.get('reply_to_message_id'),
                        m.get('media_url'), m.get('media_type'), m.get('timestamp')
                    )
                    for m in to_insert
                ]
                frappe.db.bulk_insert("Message", fields=fields, values=values)
            # single commit
            frappe.db.commit()

            # We don't know DB-assigned names from bulk_insert without requery; return message_ids as proxies
            inserted_names = [m.get('message_id') for m in to_insert if m.get('message_id')]
            logger.info(f"Batch committed {len(inserted_names)} new messages (bulk_insert), skipped {len(existing)} existing")
        except Exception as e:
            logger.error(f"Error in batch message commit: {e}")
            frappe.db.rollback()
            raise
        return inserted_names

    def _commit_rooms_sync(self, rooms: List[Dict[str, Any]]) -> List[str]:
        """Synchronously commit a batch of rooms with prefetch and bulk_insert."""
        if not rooms:
            return []
        inserted_room_ids: List[str] = []
        try:
            room_ids = [r.get('room_id') for r in rooms if r.get('room_id')]
            existing_map = {}
            if room_ids:
                existing_rows = frappe.get_list(
                    "Room",
                    filters={"room_id": ["in", room_ids]},
                    fields=["name", "room_id", "title"],
                    ignore_permissions=True,
                )
                existing_map = {r["room_id"]: r for r in existing_rows}

            to_insert = []
            title_updates = {}
            for r in rooms:
                rid = r.get('room_id')
                if not rid:
                    continue
                if rid in existing_map:
                    # prepare title updates only if changed and provided
                    new_title = r.get('title')
                    if new_title and new_title != existing_map[rid].get('title'):
                        title_updates[rid] = new_title
                else:
                    to_insert.append(r)

            if to_insert:
                fields = ["room_id","chat_id","sender_id","agent_name","title"]
                values = [
                    (
                        r.get('room_id'), r.get('chat_id'), r.get('sender_id', r.get('chat_id')),
                        r.get('agent_name'), r.get('title', f"Chat_{r.get('chat_id')}_{r.get('agent_name')}")
                    )
                    for r in to_insert
                ]
                frappe.db.bulk_insert("Room", fields=fields, values=values)
                inserted_room_ids = [r.get('room_id') for r in to_insert]

            if title_updates:
                # Grouped title update via SQL (same field, multiple rows)
                ids = list(title_updates.keys())
                params = {f"id{i}": rid for i, rid in enumerate(ids)}
                case_parts = [f"WHEN %(id{i})s THEN %(t{i})s" for i, rid in enumerate(ids)]
                for i, rid in enumerate(ids):
                    params[f"t{i}"] = title_updates[rid]
                in_list = ",".join([f"%(id{i})s" for i in range(len(ids))])
                sql = f"""
                    UPDATE `tabRoom`
                    SET title = CASE room_id {" ".join(case_parts)} END
                    WHERE room_id IN ({in_list})
                """
                frappe.db.sql(sql, params)

            frappe.db.commit()
            logger.info(f"Batch committed rooms: inserted={len(inserted_room_ids)}, updated_titles={len(title_updates)}")
        except Exception as e:
            logger.error(f"Error in batch room commit: {e}")
            frappe.db.rollback()
            raise
        return inserted_room_ids

    def _commit_agents_sync(self, agents: List[Dict[str, Any]]) -> List[str]:
        """Synchronously commit a batch of agents with prefetch and bulk_insert."""
        if not agents:
            return []
        inserted: List[str] = []
        try:
            user_ids = [a.get('user_id') for a in agents if a.get('user_id')]
            existing_map = {}
            if user_ids:
                existing_rows = frappe.get_list(
                    "Agents",
                    filters={"user_id": ["in", user_ids]},
                    fields=["name", "user_id", "agent_name", "title"],
                    ignore_permissions=True,
                )
                existing_map = {r["user_id"]: r for r in existing_rows}

            to_insert = []
            name_updates = {}
            for a in agents:
                uid = a.get('user_id')
                aname = a.get('agent_name')
                if not uid:
                    continue
                if uid in existing_map:
                    if aname and aname != existing_map[uid].get('agent_name'):
                        name_updates[uid] = aname
                else:
                    to_insert.append(a)

            if to_insert:
                fields = ["user_id","agent_name","title"]
                values = [(a.get('user_id'), a.get('agent_name'), a.get('agent_name')) for a in to_insert]
                frappe.db.bulk_insert("Agents", fields=fields, values=values)
                inserted = [a.get('user_id') for a in to_insert]

            if name_updates:
                ids = list(name_updates.keys())
                params = {f"id{i}": uid for i, uid in enumerate(ids)}
                case_parts_a = [f"WHEN %(id{i})s THEN %(n{i})s" for i, uid in enumerate(ids)]
                for i, uid in enumerate(ids):
                    params[f"n{i}"] = name_updates[uid]
                in_list = ",".join([f"%(id{i})s" for i in range(len(ids))])
                sql = f"""
                    UPDATE `tabAgents`
                    SET agent_name = CASE user_id {" ".join(case_parts_a)} END,
                        title = CASE user_id {" ".join(case_parts_a)} END
                    WHERE user_id IN ({in_list})
                """
                frappe.db.sql(sql, params)

            frappe.db.commit()
            logger.info(f"Batch committed agents: inserted={len(inserted)}, updated_names={len(name_updates)}")
        except Exception as e:
            logger.error(f"Error in batch agent commit: {e}")
            frappe.db.rollback()
            raise
        return inserted

    def _commit_updates_sync(self, updates: List[Dict[str, Any]]) -> int:
        """Synchronously commit a batch of document updates. Optimized for Message status updates."""
        if not updates:
            return 0
        updated_count = 0
        try:
            # Fast path: all are Message and fields subset of {status, message_id}
            all_message = all(u.get('doctype') == 'Message' for u in updates)
            all_simple = all(set(u.get('updates', {}).keys()) <= {'status', 'message_id'} for u in updates)
            if all_message and all_simple:
                names = [u['name'] for u in updates]
                # Build CASE parts for each field present
                have_status = any('status' in u['updates'] for u in updates)
                have_mid = any('message_id' in u['updates'] for u in updates)
                params = {}
                in_markers = []
                for i, name in enumerate(names):
                    params[f"n{i}"] = name
                    in_markers.append(f"%(n{i})s")
                set_clauses = []
                if have_status:
                    status_cases = []
                    for i, u in enumerate(updates):
                        if 'status' in u['updates']:
                            params[f"s{i}"] = u['updates']['status']
                            status_cases.append(f"WHEN %(n{i})s THEN %(s{i})s")
                    set_clauses.append(f"status = CASE name {' '.join(status_cases)} END")
                if have_mid:
                    mid_cases = []
                    for i, u in enumerate(updates):
                        if 'message_id' in u['updates']:
                            params[f"m{i}"] = u['updates']['message_id']
                            mid_cases.append(f"WHEN %(n{i})s THEN %(m{i})s")
                    set_clauses.append(f"message_id = CASE name {' '.join(mid_cases)} END")
                if set_clauses:
                    sql = f"UPDATE `tabMessage` SET {', '.join(set_clauses)} WHERE name IN ({', '.join(in_markers)})"
                    frappe.db.sql(sql, params)
                    updated_count = len(names)
                frappe.db.commit()
                logger.info(f"Batch committed {updated_count} message updates via CASE SQL")
                return updated_count

            # Fallback: per-row set_value (still batched commit)
            for u in updates:
                try:
                    frappe.db.set_value(u['doctype'], u['name'], u['updates'])
                    updated_count += 1
                except Exception as e:
                    logger.error(f"Error updating {u.get('doctype')} {u.get('name')}: {e}")
                    continue
            frappe.db.commit()
            logger.info(f"Batch committed {updated_count} updates via set_value")
        except Exception as e:
            logger.error(f"Error in batch update commit: {e}")
            frappe.db.rollback()
            raise
        return updated_count

# Global batch manager instance
_batch_manager = None

def get_batch_manager() -> DatabaseBatchManager:
    """Get or create the global batch manager instance."""
    global _batch_manager
    if _batch_manager is None:
        _batch_manager = DatabaseBatchManager()
    return _batch_manager


def bulk_validate_messages_sync(message_ids: List[str]) -> Set[str]:
    """Efficiently validate which message IDs already exist in the database (sync)."""
    if not message_ids:
        return set()
    try:
        existing_messages = frappe.get_list(
            "Message",
            filters={"message_id": ["in", message_ids]},
            fields=["message_id"],
            ignore_permissions=True
        )
        return {msg["message_id"] for msg in existing_messages}
    except Exception as e:
        logger.error(f"Error in bulk message validation: {e}")
        return set()

# Optimized batch functions for common operations

async def create_message_batch(
    chat_id: str,
    room_id: Optional[str],
    message_id: str,
    sender_id: str,
    sender_name: str,
    text: Optional[str] = None,
    media_url: Optional[str] = None,
    agent_name: Optional[str] = None,
    media_type: Optional[str] = None,
    timestamp: Optional[datetime] = None,
    receiver_id: Optional[str] = None,
    is_response_template: bool = False,
    reply_to_message_id: Optional[str] = None,
    immediate_commit: bool = False
) -> Optional[str]:
    """
    Optimized version of create_message that uses batching.
    Returns the message name if immediately committed, None if batched.
    """
    batch_manager = get_batch_manager()

    message_data = {
        'message_id': message_id,
        'room_id': room_id,
        'sender_id': sender_id,
        'sender_name': sender_name,
        'receiver_id': receiver_id or sender_id,
        'agent_name': agent_name,
        'text': text or '',
        'status': 'received' if receiver_id and not is_response_template else 'sent',
        'is_response_template': is_response_template,
        'reply_to_message_id': reply_to_message_id,
        'media_url': media_url,
        'media_type': media_type,
        'timestamp': timestamp
    }

    if immediate_commit:
        # Force immediate commit for this message
        results = batch_manager._commit_messages_sync([message_data])
        return results[0] if results else None
    else:
        # Add to batch for later commit
        await batch_manager.add_message_to_batch(message_data)
        return None

async def create_room_batch(
    chat_id: str,
    title: str,
    agent_name: str,
    immediate_commit: bool = False
) -> Optional[str]:
    """
    Optimized version of create_or_update_room that uses batching.
    """
    batch_manager = get_batch_manager()

    room_id = f"{chat_id}:{agent_name}"
    room_data = {
        'room_id': room_id,
        'chat_id': chat_id,
        'sender_id': chat_id,
        'agent_name': agent_name,
        'title': title or f"Chat_{chat_id}_{agent_name}"
    }

    if immediate_commit:
        # Force immediate commit for this room
        results = batch_manager._commit_rooms_sync([room_data])
        return room_id if results else None
    else:
        # Add to batch for later commit
        await batch_manager.add_room_to_batch(room_data)
        return room_id

async def create_agent_batch(
    user_id: str,
    agent_name: str,
    immediate_commit: bool = False
) -> Optional[str]:
    """
    Optimized version of create_or_update_agent that uses batching.
    """
    batch_manager = get_batch_manager()

    agent_data = {
        'user_id': user_id,
        'agent_name': agent_name
    }

    if immediate_commit:
        # Force immediate commit for this agent
        results = batch_manager._commit_agents_sync([agent_data])
        return results[0] if results else None
    else:
        # Add to batch for later commit
        await batch_manager.add_agent_to_batch(agent_data)
        return None

async def update_message_status_batch(
    message_name: str,
    status: str,
    message_id: Optional[str] = None,
    immediate_commit: bool = False
) -> None:
    """
    Batch update message status to avoid individual commits.
    """
    batch_manager = get_batch_manager()

    updates = {'status': status}
    if message_id:
        updates['message_id'] = message_id

    if immediate_commit:
        # Force immediate commit
        await batch_manager._execute_in_thread(
            batch_manager._commit_updates_sync,
            [{'doctype': 'Message', 'name': message_name, 'updates': updates}]
        )
    else:
        # Add to batch
        await batch_manager.add_update_to_batch('Message', message_name, updates)

def load_seen_messages_optimized(seen_file: str) -> Dict[str, Any]:
    """
    Optimized version of load_seen_messages that uses bulk validation
    instead of N+1 queries.
    """
    path = seen_file
    if not os.path.exists(path):
        return {"messages": {}, "last_processed": {}, "_file": path}

    try:
        with open(path, 'r') as f:
            data = json.load(f)
            seen_messages = data.get("messages", {})
            last_processed = data.get("last_processed", {})

        if not seen_messages:
            return {"messages": {}, "last_processed": last_processed, "_file": path}

        # Use bulk validation instead of individual queries
        message_ids = list(seen_messages.keys())
        existing_ids = bulk_validate_messages_sync(message_ids)

        # Filter to only include existing messages
        valid_seen = {key: val for key, val in seen_messages.items() if key in existing_ids}

        return {"messages": valid_seen, "last_processed": last_processed, "_file": path}

    except Exception as e:
        logger.error(f"Error loading seen messages: {e}")
        return {"messages": {}, "last_processed": {}, "_file": path}
