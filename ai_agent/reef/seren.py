import asyncio
import logging
import os
import json
from typing import Optional, Dict, Any
from telethon import TelegramClient
from telethon.errors import Session<PERSON>asswordNeededError, FloodWaitError

# Import process manager for state tracking
try:
    from .process_manager import (
        save_worker_state,
        get_worker_state,
        remove_worker_state
    )
except ImportError:
    from process_manager import (
        save_worker_state,
        get_worker_state,
        remove_worker_state
    )

class Seren:
    def __init__(self, config: Dict[str, Any]):
        self.name = config.get('name', 'seren')
        self.api_id = config['api_id']
        self.api_hash = config['api_hash']
        self.phone_number = config['phone_number']
        self.enabled = config.get('enabled', True)
        
        # Setup data directories
        self.data_dir = os.path.abspath(config.get('data_dir', 'TGCrewData'))
        self.worker_dir = os.path.join(self.data_dir, self.name)
        
        # Ensure worker directory exists
        os.makedirs(self.worker_dir, exist_ok=True)
        
        # Set up session and message tracking paths
        self.session_name = os.path.join(self.worker_dir, 
                                       config.get('session_name', f'{self.name}_session'))
        self.messages_file = os.path.join(self.worker_dir, 'processed_messages.json')
        
        self.client: Optional[TelegramClient] = None
        self.running = False
        self.logger = logging.getLogger(f'Seren.{self.name}')
        
        self.logger.debug(f"Initialized with data directory: {self.worker_dir}")
        
    async def start(self) -> bool:
        self.logger.info(f"{self.name}: Starting worker...")
        if not self.enabled:
            self.logger.info(f"{self.name} is disabled, skipping start")
            return False
            
        try:
            self.logger.debug(f"{self.name}: Creating TelegramClient instance")
            self.client = TelegramClient(self.session_name, self.api_id, self.api_hash)
            
            # Try to restore session if available
            session_file = f"{self.session_name}.session"
            if os.path.exists(session_file):
                self.logger.debug(f"{self.name}: Found existing session file at {session_file}")
            else:
                self.logger.debug(f"{self.name}: No existing session file found at {session_file}")
            
            self.logger.debug(f"{self.name}: Connecting client")
            await self.client.connect()
            
            self.logger.debug(f"{self.name}: Checking if user is authorized")
            if not await self.client.is_user_authorized():
                self.logger.error(f"{self.name}: User is not authorized. Please run add-seren to authenticate.")
                self.running = False
                return False
            
            # Update running state
            self.running = True
            
            # Save the worker state
            save_worker_state(self.name, {
                "running": True,
                "client_connected": self.client.is_connected() if self.client else False,
                "session_name": self.session_name,
                "phone_number": self.phone_number,
                "pid": os.getpid()
            })
            
            self.logger.info(f"{self.name}: Started successfully")
            return True
            
        except SessionPasswordNeededError:
            self.logger.error(f"{self.name}: 2FA password required but not implemented")
            self.running = False
            return False
        except FloodWaitError as e:
            self.logger.error(f"{self.name}: Rate limited, wait {e.seconds} seconds")
            self.running = False
            return False
        except Exception as e:
            self.logger.error(f"{self.name}: Failed to start - {str(e)}")
            self.running = False
            # Ensure we clean up the state if starting fails
            remove_worker_state(self.name)
            return False
    
    async def stop(self) -> None:
        if self.client and self.client.is_connected():
            try:
                await self.client.disconnect()
                self.logger.info(f"{self.name}: Disconnected")
            except Exception as e:
                self.logger.error(f"{self.name}: Error during disconnect - {str(e)}")
        
        self.running = False
        # Remove the worker state when stopped
        remove_worker_state(self.name)
        self.logger.info(f"{self.name}: Stopped and state cleaned up")
    
    def _load_processed_messages(self) -> set:
        """Load set of already processed message IDs."""
        if not os.path.exists(self.messages_file):
            return set()
            
        try:
            with open(self.messages_file, 'r') as f:
                return set(json.load(f))
        except (json.JSONDecodeError, IOError) as e:
            self.logger.error(f"{self.name}: Failed to load processed messages - {str(e)}")
            return set()
            
    def _save_processed_messages(self, message_ids: set) -> None:
        """Save set of processed message IDs."""
        try:
            with open(self.messages_file, 'w') as f:
                json.dump(list(message_ids), f)
        except IOError as e:
            self.logger.error(f"{self.name}: Failed to save processed messages - {str(e)}")
    
    async def send_message(self, entity, message: str, message_id: Optional[str] = None) -> bool:
        if not self.client or not self.running:
            self.logger.error(f"{self.name}: Cannot send message - worker not running")
            return False
            
        # If message_id is provided, check if it's already been processed
        if message_id:
            processed = self._load_processed_messages()
            if message_id in processed:
                self.logger.debug(f"{self.name}: Message {message_id} already processed, skipping")
                return True
                
        try:
            await self.client.send_message(entity, message)
            
            # If message_id is provided, add it to processed messages
            if message_id:
                processed = self._load_processed_messages()
                processed.add(message_id)
                self._save_processed_messages(processed)
                
            return True
        except Exception as e:
            self.logger.error(f"{self.name}: Failed to send message - {str(e)}")
            return False
    
    async def get_dialogs(self):
        if not self.client or not self.running:
            self.logger.error(f"{self.name}: Client not running")
            return []
            
        try:
            dialogs = await self.client.get_dialogs()
            return dialogs
        except Exception as e:
            self.logger.error(f"{self.name}: Failed to get dialogs - {str(e)}")
            return []
    
    def is_running(self) -> bool:
        """Check if the worker is running.
        
        This checks both the in-memory state and the persisted state to ensure accuracy.
        """
        # First check in-memory state
        in_memory_running = self.running and self.client and self.client.is_connected()
        
        # Then check persisted state
        state = get_worker_state(self.name)
        persisted_running = state and state.get("running", False)
        
        # If there's a mismatch, log it
        if in_memory_running != persisted_running:
            self.logger.debug(
                f"{self.name}: State mismatch - in_memory={in_memory_running}, "
                f"persisted={persisted_running}"
            )
        
        # If we're in memory as running, trust that
        if in_memory_running:
            return True
            
        # Otherwise, check if we have a persisted running state
        if persisted_running:
            self.logger.debug(f"{self.name}: Found running state in persistence")
            self.running = True
            return True
            
        # If we get here, the worker is not running
        self.logger.debug(f"{self.name}: Not running (in_memory={self.running}, "
                         f"has_client={self.client is not None}, "
                         f"persisted_running={persisted_running})")
        return False