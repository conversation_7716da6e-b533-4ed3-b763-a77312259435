frappe.ui.form.on('Agents', {
    refresh(frm) {
        const label = frm.doc.disabled ? 'Enable' : 'Disable';
        frm.add_custom_button(label, () => {
            if (!frm.doc.disabled) {
                // If not disabled, show confirmation dialog before disabling
                frappe.confirm(
                    __('Are you sure you want to disable this entry?'),
                    function() {
                        // User confirmed, set Disabled to true
                        frm.set_value('disabled', 1);
                        frm.save();
                        frm.set_df_property('custom_button', 'label', 'Enable');
                    },
                    function() {
                        // User cancelled, do nothing
                        frappe.show_alert(__('Action cancelled'));
                    }
                );
            } else {
                // If disabled, enable without confirmation
                frm.set_value('disabled', 0);
                frm.save();
                frm.set_df_property('custom_button', 'label', 'Disable');
            }
        })
    },
    disabled(frm) {
        // Update button text when disabled field changes
        if (frm.fields_dict.custom_button) {
            frm.set_df_property('custom_button', 'label', frm.doc.disabled ? __('Enable') : __('Disable'));
        }
    }
});
