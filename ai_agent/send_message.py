import frappe
from ai_agent.telegram_integration.telegram_integration import send_telegram_message, get_telegram_clients, start_clients
from frappe.utils import logger
import json
from typing import Optional
from datetime import datetime
import asyncio

logger = frappe.logger("telegram_integration", allow_site=True)

# Глобальна змінна для зберігання активних клієнтів
ACTIVE_CLIENTS = {}

@frappe.whitelist()
def send_message(doc_name: str = None, agent_name: str = None, receiver_id: str = None, message_text: str = None, room_id: str = None, timestamp: Optional[datetime] = None):
    agent_name_processed = None

    async def _send_message_async():
        nonlocal agent_name_processed
        try:
            logger.info(f"Запуск send_message: args={json.dumps(frappe.form_dict, indent=2)}")
            
            if not all([doc_name, agent_name, receiver_id, message_text, room_id]):
                error_msg = f"Відсутні аргументи: doc_name={doc_name}, agent_name={agent_name}, receiver_id={receiver_id}, message_text={message_text}, room_id={room_id}"
                logger.error(error_msg)
                return {"success": False, "error": "Усі аргументи (doc_name, agent_name, receiver_id, message_text, room_id) є обов’язковими"}

            agent_name_processed = agent_name.lstrip('@')
            logger.info(f"Оброблений agent_name: {agent_name_processed}")

            if not ACTIVE_CLIENTS:
                logger.info("ACTIVE_CLIENTS порожній, викликаємо start_clients")
                clients = await start_clients()
                if not clients:
                    logger.error("Не вдалося ініціалізувати клієнтів")
                    return {"success": False, "error": "Помилка ініціалізації клієнтів"}
                for client_data in clients:
                    config = client_data['config']
                    ACTIVE_CLIENTS[config['name']] = client_data['client']
                logger.info(f"ACTIVE_CLIENTS після start_clients: {list(ACTIVE_CLIENTS.keys())}")

            log_title = f"Аргументи: doc={doc_name}, agent={agent_name_processed}, receiver={receiver_id}, room={room_id}"
            if len(log_title) > 140:
                log_title = log_title[:137] + "..."
            logger.info(log_title)

            if agent_name_processed not in ["kattiecatlina", "KatSweetie"]:
                logger.error(f"Невірний agent_name: {agent_name_processed}")
                return {"success": False, "error": f"Невірний agent_name: {agent_name_processed}. Очікується kattiecatlina або KatSweetie"}

            if agent_name_processed not in ACTIVE_CLIENTS:
                logger.error(f"Клієнт {agent_name_processed} не ініціалізований. ACTIVE_CLIENTS: {list(ACTIVE_CLIENTS.keys())}")
                return {"success": False, "error": f"Клієнт {agent_name_processed} не ініціалізований"}

            doc = frappe.get_doc("Message", doc_name)
            if doc.status == "sent":
                logger.error(f"Повідомлення {doc_name} вже відправлено")
                return {"success": False, "error": "Повідомлення вже відправлено"}

            if not (receiver_id.isdigit() or receiver_id.startswith('@')):
                logger.error(f"Невірний receiver_id: {receiver_id}")
                return {"success": False, "error": f"Невірний receiver_id: {receiver_id}. Очікується числовий ID або @username"}

            client = ACTIVE_CLIENTS[agent_name_processed]
            logger.info(f"Спроба відправки повідомлення з клієнтом: {agent_name_processed}")
            sent_message = await send_telegram_message(
                client=client,
                agent_name=agent_name_processed,
                receiver_id=receiver_id,
                message_text=message_text,
                message_id=None,
                room_id=room_id,
                chat_id=room_id,
                timestamp=timestamp
            )
            logger.info(f"Відповідь Telegram: {sent_message}")
            if sent_message and hasattr(sent_message, 'id'):
                message_id = f"{room_id}:{sent_message.id}"
                frappe.db.set_value("Message", doc_name, {
                    "status": "sent",
                    "message_id": message_id
                })
                frappe.db.commit()
                logger.info(f"Повідомлення {doc_name} відправлено, ID: {message_id}")
                return {"success": True, "message_id": message_id}
            else:
                logger.error(f"Не вдалося відправити повідомлення для {doc_name}, відповідь: {sent_message}")
                return {"success": False, "error": "Не вдалося відправити повідомлення"}

        except Exception as e:
            error_msg = str(e)[:137] + "..." if len(str(e)) > 140 else str(e)
            logger.error(f"Помилка відправки повідомлення {doc_name}: {error_msg}", exc_info=True)
            return {"success": False, "error": str(e)}

    try:
        result = asyncio.run(_send_message_async())
        logger.info(f"Результат виконання: {result}")
        return result
    except Exception as e:
        logger.error(f"Помилка виконання асинхронного коду: {str(e)}", exc_info=True)
        return {"success": False, "error": f"Помилка виконання: {str(e)}"}