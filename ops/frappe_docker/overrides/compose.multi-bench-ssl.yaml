services:
  frontend:
    labels:
      # ${ROUTER}-http to use the middleware to redirect to https
      - traefik.http.routers.${ROUTER}-http.middlewares=https-redirect
      # ${ROUTER}-https the actual router using HTTPS
      # Uses the environment variable SITES
      - traefik.http.routers.${ROUTER}-https.rule=Host(${SITES?SITES not set})
      - traefik.http.routers.${ROUTER}-https.entrypoints=https
      - traefik.http.routers.${ROUTER}-https.tls=true
      # Use the service ${ROUTER} with the frontend
      - traefik.http.routers.${ROUTER}-https.service=${ROUTER}
      # Use the "le" (Let's Encrypt) resolver created below
      - traefik.http.routers.${ROUTER}-https.tls.certresolver=le
