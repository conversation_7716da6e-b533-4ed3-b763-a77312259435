{"actions": [], "allow_import": 1, "allow_rename": 1, "autoname": "field:room_id", "creation": "2025-07-08 09:31:50.354346", "doctype": "DocType", "engine": "InnoDB", "field_order": ["room_id", "sender_id", "agent_name", "title", "audio_url", "subtitles", "vue_component", "enabled", "column_break_mzfk", "messages_history_html"], "fields": [{"fieldname": "room_id", "fieldtype": "Data", "label": "Room ID", "unique": 1}, {"fieldname": "title", "fieldtype": "Data", "label": "Title"}, {"fieldname": "audio_url", "fieldtype": "Data", "hidden": 1, "label": "Audio URL"}, {"fieldname": "subtitles", "fieldtype": "Text", "label": "Subtitles"}, {"fieldname": "vue_component", "fieldtype": "HTML", "label": "Audio Waveform", "options": "&lt;script type=\"module\" src=\"/assets/ai_agent/vue_widget/subtitle-wave-player.js\"&gt;&lt;/script&gt;\n\n&lt;subtitle-wave-player style=\"border-radius: 8px; overflow: hidden;\"&gt;&lt;/subtitle-wave-player&gt;"}, {"fieldname": "column_break_mzfk", "fieldtype": "Column Break"}, {"fieldname": "messages_history_html", "fieldtype": "HTML", "label": "Messages History"}, {"fieldname": "sender_id", "fieldtype": "Data", "hidden": 1}, {"fieldname": "agent_name", "fieldtype": "Data", "label": "Agent Name"}, {"default": "0", "fieldname": "enabled", "fieldtype": "Check", "label": "Enabled"}], "grid_page_length": 50, "index_web_pages_for_search": 1, "links": [], "modified": "2025-08-01 14:22:16.507460", "modified_by": "Administrator", "module": "AI Agent", "name": "Room", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}], "row_format": "Dynamic", "sort_field": "modified", "sort_order": "DESC", "states": []}