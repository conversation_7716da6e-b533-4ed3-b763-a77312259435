"""
Configuration settings for database batch operations.
Adjust these settings based on your system's performance characteristics.
"""

# Batch size settings
DEFAULT_BATCH_SIZE = 50  # Number of operations to batch together
MESSAGE_BATCH_SIZE = 50  # Specific batch size for messages
ROOM_BATCH_SIZE = 20     # Specific batch size for rooms
AGENT_BATCH_SIZE = 10    # Specific batch size for agents
UPDATE_BATCH_SIZE = 100  # Specific batch size for updates

# Timing settings
AUTO_COMMIT_INTERVAL = 5.0  # Seconds between automatic batch commits
MAX_BATCH_AGE = 30.0        # Maximum time to hold items in batch before forcing commit

# Thread pool settings
MAX_WORKER_THREADS = 4  # Maximum number of worker threads for database operations

# Performance monitoring
ENABLE_PERFORMANCE_LOGGING = True  # Log performance metrics
LOG_SLOW_OPERATIONS = True         # Log operations that take longer than threshold
SLOW_OPERATION_THRESHOLD = 1.0     # Seconds - operations slower than this are logged

# Fallback settings
ENABLE_FALLBACK = True  # Fall back to individual operations if batch fails
MAX_RETRY_ATTEMPTS = 3  # Maximum retry attempts for failed batch operations

# Feature flags
ENABLE_MESSAGE_BATCHING = True   # Enable batching for message operations
ENABLE_ROOM_BATCHING = True      # Enable batching for room operations
ENABLE_AGENT_BATCHING = True     # Enable batching for agent operations
ENABLE_UPDATE_BATCHING = True    # Enable batching for update operations
ENABLE_BULK_VALIDATION = True    # Enable bulk message validation

# Memory management
MAX_BATCH_MEMORY_MB = 100  # Maximum memory usage for batches (approximate)
CLEANUP_INTERVAL = 300     # Seconds between batch manager cleanup

def get_batch_config():
    """Get the current batch configuration as a dictionary."""
    return {
        'batch_sizes': {
            'default': DEFAULT_BATCH_SIZE,
            'message': MESSAGE_BATCH_SIZE,
            'room': ROOM_BATCH_SIZE,
            'agent': AGENT_BATCH_SIZE,
            'update': UPDATE_BATCH_SIZE
        },
        'timing': {
            'auto_commit_interval': AUTO_COMMIT_INTERVAL,
            'max_batch_age': MAX_BATCH_AGE,
            'cleanup_interval': CLEANUP_INTERVAL
        },
        'performance': {
            'max_worker_threads': MAX_WORKER_THREADS,
            'enable_performance_logging': ENABLE_PERFORMANCE_LOGGING,
            'log_slow_operations': LOG_SLOW_OPERATIONS,
            'slow_operation_threshold': SLOW_OPERATION_THRESHOLD,
            'max_batch_memory_mb': MAX_BATCH_MEMORY_MB
        },
        'features': {
            'enable_message_batching': ENABLE_MESSAGE_BATCHING,
            'enable_room_batching': ENABLE_ROOM_BATCHING,
            'enable_agent_batching': ENABLE_AGENT_BATCHING,
            'enable_update_batching': ENABLE_UPDATE_BATCHING,
            'enable_bulk_validation': ENABLE_BULK_VALIDATION,
            'enable_fallback': ENABLE_FALLBACK
        },
        'reliability': {
            'max_retry_attempts': MAX_RETRY_ATTEMPTS
        }
    }

def update_batch_config(**kwargs):
    """Update batch configuration settings."""
    global DEFAULT_BATCH_SIZE, MESSAGE_BATCH_SIZE, ROOM_BATCH_SIZE
    global AGENT_BATCH_SIZE, UPDATE_BATCH_SIZE, AUTO_COMMIT_INTERVAL
    global MAX_BATCH_AGE, MAX_WORKER_THREADS, ENABLE_PERFORMANCE_LOGGING
    global LOG_SLOW_OPERATIONS, SLOW_OPERATION_THRESHOLD, ENABLE_FALLBACK
    global MAX_RETRY_ATTEMPTS, ENABLE_MESSAGE_BATCHING, ENABLE_ROOM_BATCHING
    global ENABLE_AGENT_BATCHING, ENABLE_UPDATE_BATCHING, ENABLE_BULK_VALIDATION
    global MAX_BATCH_MEMORY_MB, CLEANUP_INTERVAL
    
    # Update batch sizes
    if 'default_batch_size' in kwargs:
        DEFAULT_BATCH_SIZE = kwargs['default_batch_size']
    if 'message_batch_size' in kwargs:
        MESSAGE_BATCH_SIZE = kwargs['message_batch_size']
    if 'room_batch_size' in kwargs:
        ROOM_BATCH_SIZE = kwargs['room_batch_size']
    if 'agent_batch_size' in kwargs:
        AGENT_BATCH_SIZE = kwargs['agent_batch_size']
    if 'update_batch_size' in kwargs:
        UPDATE_BATCH_SIZE = kwargs['update_batch_size']
    
    # Update timing settings
    if 'auto_commit_interval' in kwargs:
        AUTO_COMMIT_INTERVAL = kwargs['auto_commit_interval']
    if 'max_batch_age' in kwargs:
        MAX_BATCH_AGE = kwargs['max_batch_age']
    if 'cleanup_interval' in kwargs:
        CLEANUP_INTERVAL = kwargs['cleanup_interval']
    
    # Update performance settings
    if 'max_worker_threads' in kwargs:
        MAX_WORKER_THREADS = kwargs['max_worker_threads']
    if 'enable_performance_logging' in kwargs:
        ENABLE_PERFORMANCE_LOGGING = kwargs['enable_performance_logging']
    if 'log_slow_operations' in kwargs:
        LOG_SLOW_OPERATIONS = kwargs['log_slow_operations']
    if 'slow_operation_threshold' in kwargs:
        SLOW_OPERATION_THRESHOLD = kwargs['slow_operation_threshold']
    if 'max_batch_memory_mb' in kwargs:
        MAX_BATCH_MEMORY_MB = kwargs['max_batch_memory_mb']
    
    # Update feature flags
    if 'enable_message_batching' in kwargs:
        ENABLE_MESSAGE_BATCHING = kwargs['enable_message_batching']
    if 'enable_room_batching' in kwargs:
        ENABLE_ROOM_BATCHING = kwargs['enable_room_batching']
    if 'enable_agent_batching' in kwargs:
        ENABLE_AGENT_BATCHING = kwargs['enable_agent_batching']
    if 'enable_update_batching' in kwargs:
        ENABLE_UPDATE_BATCHING = kwargs['enable_update_batching']
    if 'enable_bulk_validation' in kwargs:
        ENABLE_BULK_VALIDATION = kwargs['enable_bulk_validation']
    if 'enable_fallback' in kwargs:
        ENABLE_FALLBACK = kwargs['enable_fallback']
    
    # Update reliability settings
    if 'max_retry_attempts' in kwargs:
        MAX_RETRY_ATTEMPTS = kwargs['max_retry_attempts']

def get_optimal_batch_size(operation_type: str, system_load: float = 0.5) -> int:
    """
    Get optimal batch size based on operation type and current system load.
    
    Args:
        operation_type: Type of operation ('message', 'room', 'agent', 'update')
        system_load: Current system load factor (0.0 to 1.0)
    
    Returns:
        Optimal batch size for the given conditions
    """
    base_sizes = {
        'message': MESSAGE_BATCH_SIZE,
        'room': ROOM_BATCH_SIZE,
        'agent': AGENT_BATCH_SIZE,
        'update': UPDATE_BATCH_SIZE
    }
    
    base_size = base_sizes.get(operation_type, DEFAULT_BATCH_SIZE)
    
    # Adjust based on system load
    if system_load > 0.8:
        # High load - reduce batch size
        return max(1, int(base_size * 0.5))
    elif system_load < 0.3:
        # Low load - increase batch size
        return int(base_size * 1.5)
    else:
        # Normal load - use base size
        return base_size

def is_feature_enabled(feature_name: str) -> bool:
    """Check if a specific batch feature is enabled."""
    feature_flags = {
        'message_batching': ENABLE_MESSAGE_BATCHING,
        'room_batching': ENABLE_ROOM_BATCHING,
        'agent_batching': ENABLE_AGENT_BATCHING,
        'update_batching': ENABLE_UPDATE_BATCHING,
        'bulk_validation': ENABLE_BULK_VALIDATION,
        'fallback': ENABLE_FALLBACK,
        'performance_logging': ENABLE_PERFORMANCE_LOGGING,
        'slow_operation_logging': LOG_SLOW_OPERATIONS
    }
    
    return feature_flags.get(feature_name, False)

# Environment-based configuration
def load_config_from_env():
    """Load configuration from environment variables."""
    import os
    
    env_mappings = {
        'BATCH_DEFAULT_SIZE': 'default_batch_size',
        'BATCH_MESSAGE_SIZE': 'message_batch_size',
        'BATCH_ROOM_SIZE': 'room_batch_size',
        'BATCH_AGENT_SIZE': 'agent_batch_size',
        'BATCH_UPDATE_SIZE': 'update_batch_size',
        'BATCH_AUTO_COMMIT_INTERVAL': 'auto_commit_interval',
        'BATCH_MAX_WORKER_THREADS': 'max_worker_threads',
        'BATCH_ENABLE_PERFORMANCE_LOGGING': 'enable_performance_logging',
        'BATCH_ENABLE_MESSAGE_BATCHING': 'enable_message_batching',
        'BATCH_ENABLE_ROOM_BATCHING': 'enable_room_batching',
        'BATCH_ENABLE_AGENT_BATCHING': 'enable_agent_batching',
        'BATCH_ENABLE_UPDATE_BATCHING': 'enable_update_batching',
        'BATCH_ENABLE_BULK_VALIDATION': 'enable_bulk_validation'
    }
    
    config_updates = {}
    
    for env_var, config_key in env_mappings.items():
        value = os.getenv(env_var)
        if value is not None:
            # Convert to appropriate type
            if config_key.startswith('enable_'):
                config_updates[config_key] = value.lower() in ('true', '1', 'yes', 'on')
            elif 'size' in config_key or 'threads' in config_key:
                try:
                    config_updates[config_key] = int(value)
                except ValueError:
                    pass
            elif 'interval' in config_key:
                try:
                    config_updates[config_key] = float(value)
                except ValueError:
                    pass
    
    if config_updates:
        update_batch_config(**config_updates)
        return config_updates
    
    return None

# Load environment configuration on import
load_config_from_env()
