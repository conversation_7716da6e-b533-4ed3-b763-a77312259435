// frappe.ui.form.on('Message', {
//     refresh: function(frm) {
//         // Додаємо кнопку "Надіслати повідомлення"
//         frm.add_custom_button(__('Надіслати повідомлення'), function() {
//             // Відкриваємо діалогове вікно
//             let dialog = new frappe.ui.Dialog({
//                 title: __('Надіслати повідомлення'),
//                 fields: [
//                     {
//                         fieldname: 'room_id',
//                         fieldtype: 'Link',
//                         label: __('Room ID'),
//                         options: 'Room',
//                         default: frm.doc.room_id,
//                         read_only: 1
//                     },
//                     {
//                         fieldname: 'message_id',
//                         fieldtype: 'Data',
//                         label: __('Message ID'),
//                         default: `response_${frappe.utils.get_random(10)}`
//                     },
//                     {
//                         fieldname: 'sender_name',
//                         fieldtype: 'Data',
//                         label: __('Sender Name'),
//                         default: frm.doc.agent,
//                         read_only: 1
//                     },
//                     {
//                         fieldname: 'receiver',
//                         fieldtype: 'Data',
//                         label: __('Receiver'),
//                         default: frm.doc.sender_id,
//                         read_only: 1
//                     },
//                     {
//                         fieldname: 'sender_id',
//                         fieldtype: 'Data',
//                         label: __('Sender ID'),
//                         hidden: 1,
//                         default: frm.doc.agent === 'kattiecatlina' ? '7700577098' : '7683635155'
//                     },
//                     {
//                         fieldname: 'agent',
//                         fieldtype: 'Select',
//                         label: __('Agent'),
//                         options: ['kattiecatlina', 'KatSweetie'],
//                         default: frm.doc.agent,
//                         read_only: 1
//                     },
//                     {
//                         fieldname: 'is_response_template',
//                         fieldtype: 'Check',
//                         label: __('Is Response Template'),
//                         default: 0
//                     },
//                     {
//                         fieldname: 'section_break',
//                         fieldtype: 'Section Break'
//                     },
//                     {
//                         fieldname: 'text',
//                         fieldtype: 'Data',
//                         label: __('Text'),
//                         reqd: 1
//                     },
//                     {
//                         fieldname: 'status',
//                         fieldtype: 'Data',
//                         label: __('Status'),
//                         default: 'sent',
//                         read_only: 1
//                     },
//                     {
//                         fieldname: 'timestamp',
//                         fieldtype: 'Datetime',
//                         label: __('Timestamp'),
//                         default: frappe.datetime.now_datetime(),
//                         read_only: 1
//                     },
//                     {
//                         fieldname: 'column_break',
//                         fieldtype: 'Column Break'
//                     },
//                     {
//                         fieldname: 'media_url',
//                         fieldtype: 'Data',
//                         label: __('Media URL')
//                     },
//                     {
//                         fieldname: 'media_html',
//                         fieldtype: 'HTML',
//                         hidden: 1
//                     }
//                 ],
//                 primary_action_label: __('Надіслати'),
//                 primary_action(values) {
//                     // Перевірка довжини тексту
//                     if (values.text && values.text.length > 140) {
//                         frappe.msgprint(__('Текст перевищує 140 символів. Скоротіть його.'));
//                         return;
//                     }

//                     // Виклик серверного методу
//                     frappe.call({
//                         method: 'ai_agent.telegram_integration.telegram_integration.send_manual_message',
//                         args: {
//                             chat_id: frm.doc.chat_id,
//                             agent_name: values.agent,
//                             text: values.text,
//                             sender_id: values.sender_id,
//                             sender_name: values.sender_name,
//                             receiver_id: values.receiver
//                         },
//                         callback: function(r) {
//                             if (r.message.status === 'success') {
//                                 dialog.hide();
//                                 frappe.msgprint(__('Повідомлення успішно надіслано!'));
//                                 frm.refresh();
//                             } else {
//                                 frappe.msgprint(__('Не вдалося надіслати повідомлення: ') + r.message.message);
//                             }
//                         },
//                         error: function(err) {
//                             frappe.msgprint(__('Помилка надсилання повідомлення. Перевірте логи.'));
//                             console.error(err);
//                         }
//                     });
//                 }
//             });

//             dialog.show();
//         });
//     }
// });