import asyncio
import yaml
import logging
import os
import time
from typing import List, Dict, Any, Optional
try:
    from .seren import Seren
    from .process_manager import (
        get_worker_state,
        get_all_worker_states,
        remove_worker_state
    )
except ImportError:
    from seren import Seren
    from process_manager import (
        get_worker_state,
        get_all_worker_states,
        remove_worker_state
    )


class TGCrew:
    def __init__(self, config_path: str = "tg-crew.yml"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.seren_workers: List[Seren] = []
        self.running = False
        self.logger = logging.getLogger('TGCrew')
        
        self._load_config()
        self._setup_logging()
        self._create_workers()
    
    def _load_config(self) -> None:
        if not os.path.exists(self.config_path):
            raise FileNotFoundError(f"Configuration file {self.config_path} not found")
        
        try:
            with open(self.config_path, 'r') as f:
                self.config = yaml.safe_load(f)
        except yaml.YAMLError as e:
            raise ValueError(f"Invalid YAML in {self.config_path}: {e}")
        except Exception as e:
            raise Exception(f"Failed to load config: {e}")
    
    def _setup_logging(self) -> None:
        global_settings = self.config.get('global_settings', {})
        log_level = global_settings.get('log_level', 'INFO')
        
        logging.basicConfig(
            level=getattr(logging, log_level.upper()),
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
    
    def _create_workers(self) -> None:
        seren_configs = self.config.get('seren_workers', [])
        global_settings = self.config.get('global_settings', {})
        
        data_dir = global_settings.get('data_dir', 'TGCrewData')
        
        os.makedirs(data_dir, exist_ok=True)
        
        for worker_config in seren_configs:
            worker_config['data_dir'] = data_dir
        
        for config in seren_configs:
            try:
                seren = Seren(config)
                self.seren_workers.append(seren)
                self.logger.info(f"Created Seren worker: {seren.name}")
            except Exception as e:
                self.logger.error(f"Failed to create Seren worker {config.get('name', 'unknown')}: {e}")
    
    async def start_all(self) -> Dict[str, bool]:
        results = {}
        
        for seren in self.seren_workers:
            try:
                success = await seren.start()
                results[seren.name] = success
                if success:
                    self.logger.info(f"Started {seren.name}")
                else:
                    self.logger.error(f"Failed to start {seren.name}")
            except Exception as e:
                self.logger.error(f"Exception starting {seren.name}: {e}")
                results[seren.name] = False
        
        self.running = any(results.values())
        return results
    
    async def stop_all(self) -> None:
        tasks = []
        for seren in self.seren_workers:
            if seren.is_running():
                tasks.append(seren.stop())
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
        
        self.running = False
        self.logger.info("All Seren workers stopped")
    
    async def restart_all(self) -> Dict[str, bool]:
        await self.stop_all()
        await asyncio.sleep(2)
        return await self.start_all()
    
    def get_worker(self, name: str) -> Optional[Seren]:
        name_lower = name.lower()
        for seren in self.seren_workers:
            if seren.name.lower() == name_lower:
                self.logger.debug(f"Found worker '{seren.name}' (requested: '{name}')")
                return seren
        self.logger.warning(f"Worker '{name}' not found in {[w.name for w in self.seren_workers]}")
        return None
    
    def get_active_workers(self) -> List[Seren]:
        return [seren for seren in self.seren_workers if seren.is_running()]
    
    def get_worker_status(self) -> Dict[str, Dict[str, Any]]:
        """Get the status of all workers, including both in-memory and persisted states."""
        status = {}
        persisted_states = get_all_worker_states()
        for seren in self.seren_workers:
            try:
                persisted_state = persisted_states.get(seren.name, {})
                is_running = seren.is_running()
                has_client = seren.client is not None
                client_connected = has_client and seren.client.is_connected()
                
                debug_info = {
                    'has_client': has_client,
                    'client_connected': client_connected,
                    'seren_running': getattr(seren, 'running', 'N/A'),
                    'client_connected_attr': getattr(seren.client, 'connected', 'N/A') if has_client else 'No client',
                    'client_session_path': getattr(seren.client, 'session', {}).__class__.__name__ if has_client else 'No client',
                    'persisted_state': bool(persisted_state),
                    'persisted_running': persisted_state.get('running', False) if persisted_state else False,
                    'pid': persisted_state.get('pid', 'N/A') if persisted_state else 'N/A',
                    'last_update': time.ctime(persisted_state.get('timestamp', 0)) if persisted_state else 'Never'
                }
                
                effective_running = is_running or (persisted_state and persisted_state.get('running', False))
                
                status[seren.name] = {
                    'enabled': seren.enabled,
                    'running': effective_running,
                    'phone_number': getattr(seren, 'phone_number', 'N/A'),
                    'session_name': getattr(seren, 'session_name', 'N/A'),
                    'client_status': 'connected' if client_connected else 'disconnected',
                    'pid': persisted_state.get('pid', 'N/A') if persisted_state else 'N/A',
                    'debug': debug_info
                }
                
            except Exception as e:
                persisted_state = persisted_states.get(seren.name, {})
                
                status[seren.name] = {
                    'error': f"Error getting status: {str(e)}",
                    'enabled': getattr(seren, 'enabled', 'N/A'),
                    'running': persisted_state.get('running', False) if persisted_state else False,
                    'phone_number': getattr(seren, 'phone_number', 'N/A'),
                    'session_name': getattr(seren, 'session_name', 'N/A'),
                    'client_status': 'error',
                    'pid': persisted_state.get('pid', 'N/A') if persisted_state else 'N/A',
                    'debug': {
                        'exception': str(e),
                        'type': type(e).__name__,
                        'persisted_state': bool(persisted_state),
                        'persisted_running': persisted_state.get('running', False) if persisted_state else False
                    }
                }
        return status

    async def run_forever(self) -> None:
        try:
            results = await self.start_all()
            self.logger.info(f"TGCrew started. Workers status: {results}")
            
            while self.running:
                await asyncio.sleep(10)
                
                alive_count = len(self.get_active_workers())
                if alive_count == 0:
                    self.logger.warning("No active workers remaining")
                    break
                    
        except KeyboardInterrupt:
            self.logger.info("TGCrew interrupted by user")
        except Exception as e:
            self.logger.error(f"TGCrew error: {e}")
        finally:
            await self.stop_all()
            self.logger.info("TGCrew stopped")


async def main():
    crew = TGCrew()
    await crew.run_forever()


if __name__ == "__main__":
    asyncio.run(main())